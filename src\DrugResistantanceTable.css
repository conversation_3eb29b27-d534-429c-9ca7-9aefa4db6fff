.drug-resistance-container {
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
    padding: 20px;
    background-color: white;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
  }
  
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }
  
  .file-import-section {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .file-import-section button {
    padding: 0;
    margin: 0;
    outline: none;
  }
  
  .file-input {
    display: none;
  }
  
  .file-import-button {
    display: flex;
    align-items: center;
    padding: 8px 16px;
    background-color: #3b82f6;
    color: white;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.3s ease;
  }
  
  .file-import-button:hover {
    background-color: #2563eb;
  }
  
  .file-import-button svg {
    width: 20px;
    height: 20px;
    margin-right: 8px;
  }
  
  .file-name {
    color: #666;
    font-size: 0.875rem;
  }
  
  .table-title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
  }
  
  .drug-resistance-table {
    width: 100%;
    border-collapse: collapse;
  }
  
  .drug-resistance-table th,
  .drug-resistance-table td {
    border: 1px solid #ddd;
    padding: 6px;
    text-align: left;
    color: black;
    font-size: 14px;
  }
  
  .drug-resistance-table thead {
    background-color: #f9fafb;
  }
  
  .drug-resistance-table tr:hover {
    background-color: #f5f5f5;
  }
  
  .resistance-耐药 {
    color: #dc2626;
    font-weight: bold;
  }
  
  .resistance-敏感 {
    color: #16a34a;
    font-weight: bold;
  }
  
  .resistance-中介 {
    color: #eab308;
    font-weight: bold;
  }