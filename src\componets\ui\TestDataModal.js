import React from "react";
import { Modal, Table, Button } from "antd";
import { Trash2 } from "lucide-react";
const TestDataModal = ({ visible, onClose, testDataList, onDelete }) => {
  
  // Define table columns
  const columns = [
     {
      title: "检测方法",
      dataIndex: "testMethod",
      key: "testMethod",
      align: "center",
      ellipsis: true, 
    },
    {
      title: "解析结果",
      dataIndex: "testResult",
      key: "testResult",
      align: "center",
    },
    {
      title: "补充试验",
      dataIndex: "testSupplement",
      key: "testSupplement",
      align: "center",
      ellipsis: true, 
    },
    {
      title: "补充结果",
      dataIndex: "testSupplementResult",
      key: "testSupplementResult",
      align: "center",
      ellipsis: true, 
    },
   
    
    {
      title: "操作",
      key: "action",
      align: "center",
      render: (text, record, index) => (
        <Button
          type="danger"
          onClick={() => onDelete(index)}
          size="small"
          title="删除"
        >
         <Trash2 size={20}  style={{color:"black"}}/>
        </Button>
      ),
    },
   
  ];

  return (
    <Modal
      title="检测数据"
      visible={visible}
      onCancel={onClose}
      footer={[
        <Button key="close" onClick={onClose}>
          关闭
        </Button>,
      ]}
      width={700}
    >
      <Table
        columns={columns}
        dataSource={testDataList}
        pagination={false}
        rowKey={(record, index) => index}
        bordered
       
      />
    </Modal>
  );
};

export default TestDataModal;
