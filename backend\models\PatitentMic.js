const mongoose = require('mongoose');

const patientMicSchema = new mongoose.Schema({
  patient_id: { type: String, required: true },  // 患者ID，必须唯一
  bacteriaName: { type: String, required: true },  // 细菌名称
  selectedAgent: { type: String, required: true },  // 抗菌药物名称
  'S (mm)': { type: String },  // 敏感范围
  'S (μg/mL)': { type: String },  // 敏感范围
  'SDD (μg/mL)': { type: String },  // 半敏感范围
  'I (μg/mL)': { type: String },  // 中介范围
  'R (μg/mL)': { type: String },  // 耐药范围
  micValue: { type: Number, required: true },  // MIC 值，应该是数值
  sensitivity: { type: String, required: true },  // 药物敏感性 (如: "敏感", "耐药")
  comments:{type: String},
  remark:{type: String},
  create_time: { type: Date, default: Date.now },  // 创建时间
  update_time: { type: Date, default: Date.now }  // 更新时间
}, { collection: 'PatientMic' });

// 在保存或更新时自动更新更新时间
patientMicSchema.pre('save', function (next) {
  this.update_time = Date.now();
  next();
});

module.exports = mongoose.model('PatientMic', patientMicSchema);
