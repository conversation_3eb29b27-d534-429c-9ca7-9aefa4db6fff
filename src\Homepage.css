/* Homepage.css */

.homepage {
    border-radius: 25px;
    background-size: cover; /* 背景覆盖 */
    color: #b0b0b0; /* 深灰色字体 */
    text-align: center;
    padding: 20px;
    font-family: '<PERSON>o', sans-serif; /* 使用现代字体 */
    height: 80%; /* 满屏高度 */
    margin-top: 100px;
}

.title {
    font-size: 2.5em; /* 中文标题 */
    margin-bottom: 10px;
    
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5); /* 标题阴影 */
}

.subtitle {
    font-size: 16px; /* 英文标题更小 */
    color: #668866; /* 深灰色 */
    line-height: 1.2; /* 调整行高以保持良好的间距 */
}


.company-info {
    max-width: 600px; /* 限制公司简介的最大宽度 */
    margin: 0 auto; /* 水平居中 */
    text-align: left; /* 左对齐 */
    font-size: 0.7em; /* 较小字体 */
   
    padding: 20px;
    border-radius: 8px; /* 圆角效果 */
}

.footer {
    margin-top: 30px;
    font-size: 0.8em; /* 较小字体 */
    color: #b0b0b0; /* 深灰色 */
    border-top: 1px solid rgba(255, 255, 255, 0.2); /* 顶部边框 */
    padding-top: 10px;
}

.footer a {
    color: #00ccff; /* 版权链接颜色 */
    text-decoration: none; /* 去掉下划线 */
}

.footer a:hover {
    text-decoration: underline; /* 悬停时下划线 */
}
