import React, { useState, useEffect } from 'react';
import { Modal, Input, Table, Button } from 'antd';
import './OrganismSelectModal.css';

const OrganismSelectModal = ({ visible, onClose, onSelect }) => {
  const [organisms, setOrganisms] = useState([]);
  const [loading, setLoading] = useState(false);
  const [search, setSearch] = useState('');
  const BASE_URL = process.env.REACT_APP_BASE_URL;

  useEffect(() => {
    if (visible) {
      setLoading(true);
      fetch(`${BASE_URL}api/organisms`)
        .then(res => res.json())
        .then(data => {
          setOrganisms(data);
          setLoading(false);
        });
    }
  }, [visible, BASE_URL]);

  const filtered = organisms.filter(o =>
    o.DISPLAY_NAME.toLowerCase().includes(search.toLowerCase()) ||
    o.ORGANISM_CODE.toLowerCase().includes(search.toLowerCase())
  );

  const columns = [
    {
      title: '菌株名称',
      dataIndex: 'DISPLAY_NAME',
      key: 'DISPLAY_NAME',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '代码',
      dataIndex: 'ORGANISM_CODE',
      key: 'ORGANISM_CODE',
      align: 'center',
      ellipsis: true,
    },
    {
      title: '操作',
      key: 'action',
      align: 'center',
      render: (text, record) => (
        <Button type="link" onClick={() => { onSelect(record); onClose(); }}>选择</Button>
      ),
    },
  ];

  return (
    <Modal
      title="选择菌株"
      visible={visible}
      onCancel={onClose}
      footer={[
        <Button key="close" onClick={onClose}>关闭</Button>,
      ]}
      width={600}
      centered
      destroyOnClose
    >
      <Input
        placeholder="搜索菌株名称或代码"
        value={search}
        onChange={e => setSearch(e.target.value)}
        style={{ marginBottom: 12 }}
      />
      <Table
        columns={columns}
        dataSource={filtered}
        loading={loading}
        pagination={{ pageSize: 10 }}
        rowKey={record => record.ORGANISM_CODE}
        bordered
        size="small"
      />
    </Modal>
  );
};

export default OrganismSelectModal;
