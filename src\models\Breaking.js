class Breaking {
    constructor(data = {}) {
      this.id = data.id || '';
      this.antibioticCode = data.antibioticCode || '';
      this.antimicrobialAgent = data.antimicrobialAgent || '';
      this.diskContent = data.diskContent || '';
      this.s_mm = data.s_mm || '';
      this.sdd_mm = data.sdd_mm || '';
      this.i_mm = data.i_mm || '';
      this.r_mm = data.r_mm || '';
      this.s_ug = data.s_ug || '';
      this.sdd_ug = data.sdd_ug || '';
      this.i_ug = data.i_ug || '';
      this.r_ug = data.r_ug || '';
      this.bacterId = data.bacterId || '';
      this.bacteriaName = data.bacteriaName || '';
      this.comments = data.comments || '';
      this.newId = data.newId || '';
      this.sensitivityMethod = data.sensitivityMethod || '';
      this.nickName = data.nickName || '';
      this.createDate = data.createDate || new Date();
      this.updateDate = data.updateDate || new Date();
    }
  
    update(data = {}) {
      Object.keys(data).forEach((key) => {
        if (this.hasOwnProperty(key)) {
          this[key] = data[key];
        }
      });
      this.updateDate = new Date();
    }
  
    toJSON() {
      return {
        'Antimicrobial Agent': this.antimicrobialAgent,
        'Disk Content': this.diskContent,
        'S (mm)': this.s_mm,
        'SDD (mm)': this.sdd_mm,
        'I (mm)': this.i_mm,
        'R (mm)': this.r_mm,
        'S (μg/mL)': this.s_ug,
        'SDD (μg/mL)': this.sdd_ug,
        'I (μg/mL)': this.i_ug,
        'R (μg/mL)': this.r_ug,
        'BacterId': this.bacterId,
        'Comments': this.comments,
        'newId': this.newId,
        'nickName': this.nickName,
       
      };
    }
  }

  export default Breaking;
  
  