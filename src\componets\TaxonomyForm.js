import React, { useEffect } from 'react';
import { Form, Input, Button } from 'antd';

const TaxonomyForm = ({ onSubmit, initialValues }) => {
  const [form] = Form.useForm();
  
  // 当 initialValues 变化时，重置表单
  useEffect(() => {
    form.resetFields();  // 清空或重设表单数据
    if (initialValues) {
      initialValues = {
        name: initialValues.title, // Use `title` as the name
        key: initialValues.key,    // Use `key` if needed
      };
      form.setFieldsValue(initialValues);  // 设置表单初始值
    }
  }, [initialValues, form]);

  const handleFinish = (values) => {
    onSubmit(values);  // 提交表单
    form.resetFields();  // 提交后重置表单
  };

  return (
    <Form form={form} onFinish={handleFinish}>
      <Form.Item
        name="name"
        label="细菌名称"
        rules={[{ required: true, message: '请输入菌株名称' }]}
      >
        <Input placeholder="请输入菌株名称" />
      </Form.Item>
     
      {/* 添加更多字段，根据需求 */}
      <Form.Item name="key">
       
      </Form.Item>
      <Form.Item>
        <Button type="primary" htmlType="submit">
          {initialValues ? '保存修改' : '添加菌株'}
        </Button>
      </Form.Item>
      
    </Form>
   
  );
};

export default TaxonomyForm;
