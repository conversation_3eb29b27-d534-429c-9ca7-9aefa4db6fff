import React, { useState, useEffect } from 'react';
import { Table, Input, Button, Form, message,Select  } from 'antd';
import axios from 'axios';
import '../QueryForm.css'; 
const AntibioticsManager = () => {
  const [form] = Form.useForm();  // 表单实例
  const [data, setData] = useState([]);  // 存储从API获取的数据
  const [loading, setLoading] = useState(false);  // 加载状态
  const BASE_URL = process.env.REACT_APP_BASE_URL;
  // 获取抗生素数据
  const fetchData = async () => {
    try {
      setLoading(true);
      const response = await axios.get(`${BASE_URL}antibiotics`);
      setData(response.data);
    } catch (error) {
      message.error('加载抗生素数据失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();  // 页面加载时获取数据
  }, []);

  const handleReset = () => {
    form.resetFields();  // 清空所有输入框
  };


  // 查询抗生素数据
  const handleQuery = async () => {
    try {
      const values = form.getFieldsValue();
      if (!values.ANTI_CODE) {
        message.warning('请输入抗生素代码');
        return;
      }

      setLoading(true);

      const response = await axios.get(`${BASE_URL}antibiotics/${values.ANTI_CODE.toUpperCase()}`);
      
      if (response.data) {
        const formData = {
          ...response.data,
          IS_DISABLED: response.data.IS_DISABLED ? 1 : 0  // 转换为数字 1 或 0
        };
        form.setFieldsValue(formData);
        message.success('查询成功');
      } else {
        message.warning('未找到相关数据');
      }

    } catch (error) {
      message.error('查询失败：' + (error.response?.data?.message || '未找到该抗生素'));
    } finally {
      setLoading(false);
    }
  };
  // 保存抗生素数据
  const handleSave = async () => {
    try {
      const values = form.getFieldsValue();
      await axios.post(`${BASE_URL}api/antibiotics`, values);
      message.success('保存成功');
      fetchData();  // 保存后重新加载数据
      form.resetFields();  // 清空表单
    } catch (error) {
      message.error('保存失败');
    }
  };

  const handleDelete = async()=>{
    try {
      const values = form.getFieldsValue();
      const response = await axios.delete(`${BASE_URL}antibiotics/${values.ANTI_CODE}`);
      fetchData();  
      form.resetFields(); 
    } catch (error) {
      message.error('查询失败，未找到该抗生素');
    }
  };

  // 表格列定义
  const columns = [
    {
      title: '抗生素代码',
      dataIndex: 'ANTI_CODE',
      key: 'ANTI_CODE',
    },
    {
      title: '中文名称',
      dataIndex: 'DISPLAY_NAME',
      key: 'DISPLAY_NAME',
    },
    {
      title: '英文名称',
      dataIndex: 'DISPLAY_NAME_EN',
      key: 'DISPLAY_NAME_EN',
    },
    // {
    //   title: '抗生素类别',
    //   dataIndex: 'ANTI_Class',
    //   key: 'ANTI_Class',
    // },
    // {
    //   title: '是否禁用',
    //   dataIndex: 'IS_DISABLED',
    //   key: 'IS_DISABLED',
    //   render: (text) => (text ? '是' : '否'),
    // },
    // {
    //   title: '创建时间',
    //   dataIndex: 'CREATE_TIME',
    //   key: 'CREATE_TIME',
    // },
    // {
    //   title: '更新时间',
    //   dataIndex: 'UPDATE_TIME',
    //   key: 'UPDATE_TIME',
    // },
  ];

  return (
    <div  className="container">
      <Form form={form} layout="vertical">
        <Form.Item label={<span style={{ color: 'white' }}>抗生素代码</span>} name="ANTI_CODE" rules={[{ required: true, message: '请输入抗生素代码' }]}>
          <Input placeholder="输入抗生素代码" className='mic-input' />
        </Form.Item>
        <Form.Item  label={<span style={{ color: 'white' }}>中文名称</span>} name="DISPLAY_NAME" rules={[{ required: true, message: '请输入中文名称' }]}>
          <Input placeholder="输入中文名称" className='mic-input' />
        </Form.Item>
        <Form.Item  label={<span style={{ color: 'white' }}>英文名称</span>} name="DISPLAY_NAME_EN">
          <Input placeholder="输入英文名称" className='mic-input'  />
        </Form.Item>
        {/* <Form.Item  label={<span style={{ color: 'white' }}>抗生素类别</span>} name="ANTI_Class">
          <Input placeholder="输入抗生素类别" className='mic-input'  />
        </Form.Item>
        <Form.Item  label={<span style={{ color: 'white' }}>是否禁用</span>}  name="IS_DISABLED">
        <Select placeholder="请选择是否禁用" style={{ width:'80%',boxSizing:'border-box'}} >
        <Select.Option value={1}>是</Select.Option>
        <Select.Option value={0}>否</Select.Option>
      </Select>
        </Form.Item> */}

        <div style={{ display: 'flex', justifyContent: 'space-between', width:'80%' }}>
          <Button type="primary" onClick={handleQuery}>
            查询
          </Button>
          <Button type="primary" onClick={handleSave}>
            保存
          </Button>

          <Button type="primary" onClick={handleDelete}>
            删除
          </Button>
          <Button onClick={handleReset} style={{ marginLeft: 8 }}>
            重置
          </Button>
        </div>
      </Form>

      <Table
        columns={columns}
        dataSource={data}
        rowKey="_id"
        loading={loading}
        scroll={{ x: 1000 }}  // 允许横向滚动，限制宽度
        style={{ marginTop: '20px',width:'80%' }}
      />
    </div>
  );
};

export default AntibioticsManager;
