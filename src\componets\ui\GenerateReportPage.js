import React, { useState } from "react";
import { Select, Checkbox, Button } from "antd";

const { Option } = Select;

const GenerateReportPage = ({ specimenNumbers, onGenerateReport }) => {
  const [selectedSpecimen, setSelectedSpecimen] = useState(null);
  const [selectedAuthorities, setSelectedAuthorities] = useState([]);

  const authorities = ["CLSI", "EUCAST", "FDA", "SELF"];

  const handleGenerateReport = () => {
    if (selectedSpecimen && selectedAuthorities.length > 0) {
      onGenerateReport(selectedSpecimen, selectedAuthorities);
    }
  };
 
  return (
    <div style={{ 
       padding: "10px", 
      fontSize: "14px", 
      display: "flex", 
      alignItems: "center", 
      gap: "20px",
      color: "white" 
    }}>
      <Select
        style={{ width: "200px" }}
        placeholder="请选标本编号"
        onChange={(value) => setSelectedSpecimen(value)}
      >
        {specimenNumbers.map((item) => (
          <Option key={item} value={item}>
            {item}
          </Option>
        ))}
      </Select>

      <div style={{ display: "flex", alignItems: "center", gap: "20px", width:"100%" }}>
        <Checkbox.Group
          style={{ display: "flex", flexDirection: "row", gap: "10px" }}
          options={authorities.map(auth => ({
            label: <span style={{ color: "white" }}>{auth}</span>,
            value: auth
          }))}
          onChange={setSelectedAuthorities}
        />
        <Button type="primary" onClick={handleGenerateReport}>
          生成报告
        </Button>
      </div>
    </div>
  );
};

export default GenerateReportPage;