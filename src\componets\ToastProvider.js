import React, { 
    useState, 
    useEffect, 
    createContext, 
    useContext 
  } from 'react';
  
  // Toast组件
  const Toast = ({ 
    message = '', 
    duration = 5000, 
    onClose,
    soundUrl=`${process.env.PUBLIC_URL}/voice_on.mp3`
    
  }) => {
    const [visible, setVisible] = useState(true);
  
  const playSound = (soundUrl) => {
  const audio = new Audio(soundUrl);

  // Wait until the audio is fully loaded
  audio.addEventListener('canplaythrough', () => {
    audio.play().catch((error) => console.error('Audio playback failed:', error));
  });

  // Handle errors during loading
  audio.addEventListener('error', (error) => {
    console.error('Audio loading failed:', error);
  });
};

    useEffect(() => {
        playSound(soundUrl);
        const timer = setTimeout(() => {
        setVisible(false);
         onClose && onClose();
      }, duration, onClose, soundUrl);
  
      return () => clearTimeout(timer);
    }, [duration, onClose]);
  
    if (!visible) return null;
  
    return (
      <div 
        style={{
          position: 'fixed',
          top: '10px', // Position near the top of the page
          right: '10px', // Initial right position
          backgroundColor: '#FFC000', // Yellow background
          color: 'black', // Black text
          padding: '10px 20px',
          borderRadius: '5px',
          boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
          zIndex: 1000,
          maxWidth: '300px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          fontSize:'16px',
          animation: 'slideInRight 0.5s ease-out, slideOutLeft 0.5s ease-in 2s forwards',
        }}
      >
        <span>⚠️</span>
        <span style={{ flexGrow: 1, marginLeft: '10px' }}>{message}</span>
      </div>
    );
  };
  
  // 创建Toast上下文
  const ToastContext = createContext(null);
  
  // Toast提供者组件
  export const ToastProvider = ({ children }) => {
    const [toast, setToast] = useState({
      message: '',
      visible: false
    });
  
    // 触发Toast的方法
    const showToast = (message) => {
      setToast({ message, visible: true });
    };
  
    // 关闭Toast的方法
    const hideToast = () => {
      setToast({ message: '', visible: false });
    };
  
    return (
      <ToastContext.Provider value={{ showToast, hideToast }}>
        {children}
        {toast.visible && (
          <Toast 
            message={toast.message} 
            onClose={hideToast}
          />
        )}
            <style>
            {`
                @keyframes slideInRight {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
                }

                @keyframes slideOutLeft {
                from {
                    transform: translateX(0);
                    opacity: 1;
                }
                to {
                    transform: translateX(-100%);
                    opacity: 0;
                }
                }
            `}
            </style>

      </ToastContext.Provider>
    );
  };
  
  // 自定义Hook，用于在组件中触发Toast
  export const useToast = () => {
    const context = useContext(ToastContext);
    if (!context) {
      throw new Error('useToast must be used within a ToastProvider');
    }
    return context;
  };