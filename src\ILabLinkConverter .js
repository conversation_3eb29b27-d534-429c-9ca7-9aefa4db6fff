import React, { useState,useEffect } from 'react';
import CustomDialog from './componets/CustomDialog';
import './ilab-converter-styles.css';
import { useSharedState } from './SharedStateContext';

const ILabLinkConverter = () => {
  const [dialogVisible, setDialogVisible] = useState(false);
   const [hintMessage,setHintMessage] = useState('');
  const { state, dispatch } = useSharedState();
  const [records, setRecords] = useState([]);
 
 
  const [currentRecord, setCurrentRecord] = useState({
    specimenNumber: '',
    patientId: '',
    patientName: '',
    gender: '',
    age: '',
    department: '',
    specimenType: '',
    specimenDate: '',
    bacteriaName: '',
    antibioticName: '',
    testMethod: '',
    testResult: '',
    interpretation: ''
  });

  const handleFileChange = async (event) => {
    const file = event.target.files[0];
    
    if (!file) return;
    
    if (!file.name.toLowerCase().endsWith('.dbf')) {
      setHintMessage('请选择 DBF 文件');
      setDialogVisible(true);
      return;
    }

    const formData = new FormData();
    formData.append('file', file);

    try {
      const response = await fetch("http://123.60.62.73:5000/upload-dbf", {
        method: 'POST',
        body: formData,
      });

      if (response.ok) {
        const result = await response.json();
        // console.log("DBF Data:", result.data);
        
        const mappedData = result.data.map((item) => ({
            specimenNumber: item['SPECIMENNU'],
            patientId: item['PATIENTID'],
            patientName: item['PATIENTNAM'],
            gender: item['GENDER'],
            age:item['AGE'],
            department:item['DEPARTMENT'],
            specimenType:item['SPECIMENTY'],
            specimenDate:item['SPECIMENDA'],
            bacteriaName:item['BACTERIANA'],
            antibioticName:item['ANTIBIOTIC'],
            testMethod:item['TESTMETHOD'],
            testResult:item['TESTRESULT'],
            interpretation:item['INTERPRETA']
        }));
        
        setRecords(prevRecords => [...prevRecords, ...mappedData]); // Store JSON data in state for display
      } else {
        const error = await response.json();
        alert("Error: " + error.error);
      }
    } catch (error) {
      console.log(`错误: ${error.message}`);
    } finally {
       event.target.value = '';
    }
  };


  const requiredFields = [
    { key: 'specimenNumber', label: '标本编号' },
    { key: 'patientId', label: '病员号' },
    { key: 'patientName', label: '患者姓名' },
    { key: 'gender', label: '性别' },
    { key: 'age', label: '年龄' },
    { key: 'department', label: '科室名称' },
    { key: 'specimenType', label: '标本种类' },
    { key: 'specimenDate', label: '标本日期' },
    { key: 'bacteriaName', label: '细菌名称' },
    { key: 'antibioticName', label: '抗生素名称' },
    { key: 'testMethod', label: '药敏方法' },
    { key: 'testResult', label: '药敏结果' },
    { key: 'interpretation', label: '判读结果' }
  ];
 
  useEffect(() => {
    if (state.mic && state.patient) {
      const existingRecord = state.patient
        .getTestData()
        .find((data) => data.specimenNumber === state.mic[0]?.specimenNumber);
  
      const sampleType = existingRecord?.sampleType || "";
      const rawDate = state.patient.specimenNumber.substring(0, 8);
        const formattedDate = `${rawDate.substring(0, 4)}-${rawDate.substring(4, 6)}-${rawDate.substring(6, 8)}`;

      const newRecords = state.mic.map((record) => ({
        specimenNumber: record.specimenNumber,
        patientId: state.patient.patient_id,
        patientName: state.patient.name,
        gender: state.patient.gender,
        age: state.patient.age,
        department: state.patient.department,
        specimenType: state.patient.sampleType,
        specimenDate:formattedDate,
        bacteriaName: record.Bacteria,
        antibioticName: record.Agent,
        testMethod: record.selectedMethod,
        testResult: record.MIC,
        interpretation: record.SORT,
      }));
  
      setRecords((prev) => {
        // Combine previous and new records
        const combinedRecords = [...prev, ...newRecords];
  
        // Deduplicate based on specimenNumber and antibioticName
        const uniqueRecords = combinedRecords.reduce((acc, curr) => {
          const key = `${curr.specimenNumber}-${curr.antibioticName}`;
          if (!acc.map.has(key)) {
            acc.map.set(key, true);
            acc.records.push(curr);
          }
          return acc;
        }, { map: new Map(), records: [] });
  
        return uniqueRecords.records;
      });
    }
  }, [state.mic, state.patient]);
  
  
  const handleInputChange = (field, value) => {
    setCurrentRecord(prev => ({
      ...prev,
      [field]: value
    }));
  };
  
  const handleClose = () => {
    setDialogVisible(false);
  };


  const addRecord = () => {
    if (Object.values(currentRecord).some(value => !value)) {
      setHintMessage('请填写所有必填字段');
      setDialogVisible(true);
      return;
    }
    
    setRecords(prev => [...prev, { ...currentRecord }]);
    // Keep patient and specimen info for next entry
    setCurrentRecord({
      ...currentRecord,
      antibioticName: '',
      testMethod: '',
      testResult: '',
      interpretation: ''
    });
  };

  const deleteRecord = (index) => {
    setRecords(prev => prev.filter((_, i) => i !== index));
  };

  const generateCSV = () => {
    if (records.length === 0) {
      setHintMessage('请先添加数据记录');
      setDialogVisible(true);
      return;
    }
  
    // Function to escape CSV fields
    const escapeCSVField = (field) => {
      if (field === null || field === undefined) return '';
      const value = field.toString();
      // Wrap field in quotes if it contains commas, quotes, or newlines
      if (value.includes(',') || value.includes('"') || value.includes('\n')) {
        return `"${value.replace(/"/g, '""')}"`; // Escape double quotes by doubling them
      }
      return value;
    };
  
    // Generate headers from requiredFields
    const headers = requiredFields.map((field) => escapeCSVField(field.label)).join(',');
  
    // Generate rows with sorted and properly formatted data
    const rows = records
      .sort((a, b) => {
        if (a.specimenNumber === b.specimenNumber) {
          return a.bacteriaName.localeCompare(b.bacteriaName);
        }
        return a.specimenNumber.localeCompare(b.specimenNumber);
      })
      .map((record) => 
        requiredFields.map((field) => escapeCSVField(record[field.key])).join(',')
      );
  
    // Combine headers and rows to form CSV content
    const csvContent = [headers, ...rows].join('\n');
  
    // Create a blob and trigger download
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = 'iLabLink_data.csv';
    document.body.appendChild(link); // Required for Firefox
    link.click();
    document.body.removeChild(link); // Clean up
  };
  

  const handleUpload= async ()=>{
    try {
      const data={
        "fields": [
            { "specimenNumber": "标本编号" },
            { "patientId": "病员号" },
            { "patientName": "患者姓名" },
            { "gender": "性别" },
            { "age": "年龄" },
            { "department": "科室名称" },
            { "specimenType": "标本种类" },
            { "specimenDate": "标本日期" },
            { "bacteriaName": "细菌名称" },
            { "antibioticName": "抗生素名称" },
            { "testMethod": "药敏方法" },
            { "testResult": "药敏结果" },
            { "interpretation": "判读结果" }
        ],
        "data": records
    }
    
       const response = await fetch("http://123.60.62.73:5000/create-dbf", {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });
  
      if (response.ok) {
        const blob = await response.blob(); // Convert the response to a Blob
        const url = window.URL.createObjectURL(blob); // Create a download URL
        const a = document.createElement('a'); // Create a link element
        a.href = url;
        a.download = 'output.dbf'; // File name for the download
        document.body.appendChild(a);
        a.click(); // Programmatically click the link to trigger download
        a.remove(); // Remove the link element
      }
      
     
    } catch (error) {
     
    }finally {
    
     
    }
  };



  return (
    <div className="converter-container">
      <h1 className="converter-title">iLabLink 数据转换工具</h1>
      
      <div className="button-group">
        <label className="btn btn-danger">
          导入dbf
          <input 
            type="file" 
            accept=".dbf" 
            onChange={handleFileChange} 
            className="upload-input"
          />
        </label>

        <button 
          className="btn btn-primary" 
          onClick={handleUpload} 
          disabled={records.length === 0}
        >
          导出dbf
        </button>
       
      </div>
     
      <div className="input-grid">
        {requiredFields.map(field => (
          <input
            key={field.key}
            className="input-field"
            placeholder={field.label}
            value={currentRecord[field.key]}
            onChange={(e) => handleInputChange(field.key, e.target.value)}
          />
        ))}
      </div>

      <button className="btn btn-primary" onClick={addRecord}>
        添加记录
      </button>

      <div className="table-container">
        <table className="result-table">
          <thead>
            <tr>
              {requiredFields.map(field => (
                <th key={field.key}>{field.label}</th>
              ))}
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            {records.map((record, index) => (
              <tr key={index}>
                {requiredFields.map(field => (
                  <td  key={field.key}>{record[field.key]}</td>
                ))}
                <td>
                  <button 
                    className="btn btn-danger" 
                    onClick={() => deleteRecord(index)}
                  >
                    删除
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
        {dialogVisible && (
              <CustomDialog
                message={hintMessage}
                onClose={handleClose} />
            )}
      </div>
    </div>
  );
};

export default ILabLinkConverter;