const mongoose = require('mongoose');

const antibioticsSchema = new mongoose.Schema({
  ANTI_CODE: { type: String, required: true, unique: true },  // 抗生素代码，唯一
  DISPLAY_NAME: { type: String, required: true },  // 中文显示名称
  DISPLAY_NAME_EN: { type: String },  // 英文显示名称
  ANTI_Class: { type: String },  // 抗生素类别
  IS_DISABLED: { type: Boolean, default: false },  // 是否禁用，使用 Boolean 类型
  DISABLE_TIME: { type: Date },  // 禁用时间
  CREATE_TIME: { type: Date, default: Date.now },  // 创建时间，默认当前时间
  UPDATE_TIME: { type: Date, default: Date.now }  // 更新时间，默认当前时间
}, { collection: 't_dict_antibiotics' });

// 保存前更新更新时间
antibioticsSchema.pre('save', function (next) {
  this.UPDATE_TIME = Date.now();
  next();
});

module.exports = mongoose.model('Antibiotics', antibioticsSchema);
