/* ReportPage.css */

.report-container {
    width: 40%; /* Reduced width by half */
    margin: 100px auto 0 auto !important;
    font-family: "Arial", sans-serif;
    color: #000;
    background-color: #fff;
    padding: 20px;
   
  }
  
  .title {
    text-align: center;
    font-size: 22px; /* Slightly smaller title */
    margin-bottom: 15px; /* Reduced bottom margin */
  }
  
  .patient-info, .info-line {
    display: flex;
    justify-content: space-between;
    line-height: 1.2; /* Normal line spacing */
    font-weight: bold; /* Bold text */
    font-size: 14px; /* Smaller font size */
    color: #000; /* Black color */
    text-align: left; /* Left align */
    margin-bottom: 3px; /* Reduced margin between lines */
  }
  
  .patient-info p, .info-line p {
    flex: 1;
    margin: 0 5px; /* Reduced column spacing */
  }
  
  .result {
    line-height: 1.2; /* Normal line spacing */
    font-weight: bold; /* Bold text */
    font-size: 14px; /* Smaller font size */
    color: #000; /* Black color */
    text-align: left; /* Left align */
    margin-bottom: 5px; /* Reduced margin */
  }
  
  .result .bacterium-name {
    color: red; /* Red color for bacterium name */
  }
  
  .antibiotic-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 15px; /* Reduced top margin */
  }
  
  .antibiotic-table th, .antibiotic-table td {
    border: 1px solid #000;
    padding: 6px; /* Reduced padding for tighter spacing */
    text-align: center;
    font-size: 13px; /* Reduced font size for table */
    
  }

  .antibiotic-table td:last-child {
    white-space: normal; /* 最后一列折行 */
    word-wrap: break-word;
  }


  
  .notes {
    font-size: 12px; /* Small font size for notes */
    color: blue; /* Blue color for notes */
    margin-top: 5px; /* Reduced top margin */
    text-align: left; /* Left align notes */
  }

  
  .patient-selection-content {
    display: flex;
    align-items: center;
    color: #4b5563;
    font-size: 16px;
  }
  
  .patient-selection-text {
    margin-left: 0.5rem;
  }

  .remark-input {
    width: 100%;
     padding: 10x;
    font-size: 14px;
    margin-bottom: 5px;
    box-sizing: border-box;
  }
  
  
  
  
  
 