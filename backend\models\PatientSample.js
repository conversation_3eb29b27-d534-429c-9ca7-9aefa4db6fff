const mongoose = require('mongoose');

const patientSampleSchema = new mongoose.Schema({
  patient_id: { type: String, required: true },  // 患者ID
  originalAgent: { type: String },  // 涂片结果
  secondAgent: { type: String },  // 测试方法
  result: { type: String },  // 培训结果
  create_time: { type: Date, default: Date.now },  // 创建时间
  update_time: { type: Date, default: Date.now }  // 更新时间
}, { collection: 'patientSample' });

// 在保存数据前更新更新时间
patientSampleSchema.pre('save', function (next) {
  this.update_time = Date.now();
  next();
});

patientSampleSchema.pre('findOneAndUpdate', function (next) {
  this.set({ update_time: Date.now() }); // 更新 `update_time`
  next();
});

module.exports = mongoose.model('PatientSample', patientSampleSchema);

