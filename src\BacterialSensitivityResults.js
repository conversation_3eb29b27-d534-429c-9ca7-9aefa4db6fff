import React, { useState,useEffect,useRef } from 'react';
import { ShieldQuestion, Search} from 'lucide-react';
import { useSharedState } from './SharedStateContext';
import { Lightbulb,BellRing,FlaskConical} from 'lucide-react';
import { Select, Checkbox, Button } from "antd";
import MICData from './models/MICData';
import CustomDialog from './componets/CustomDialog';
import './BacterialSensitivity.css';
import './CompoundTable.css';

const BacterialSensitivityResults = () => {
  const { state, dispatch } = useSharedState(); 
  const [selectedSpecimen, setSelectedSpecimen] = useState('');
  const [selectedTestMethod, setSelectedTestMethod] = useState('');
  const [selectedSpecType, setSelectedSpecType] = useState('');
  const [bacteriaId, setBacteriaId] = useState('');
  const [error, setError] = useState('');
  const [bacteriaData, setBacteriaData] = useState(null);
  const [result, setResult] = useState(null);
  const [results, setResults] = useState([]);
  const [hintMessage,setHintMessage] = useState('');
  const [dialogVisible, setDialogVisible] = useState(false);
  const [selectedAuthorities, setSelectedAuthorities] = useState([]);
  const authorities = ["CLSI", "EUCAST", "FDA", "SELF"];

  const resultContainerRef = useRef(null);

  const BASE_URL = process.env.REACT_APP_BASE_URL;
  useEffect( () => {
    if(selectedTestMethod && selectedSpecType){
       fetchBacteriaData();
    }
  }, [selectedTestMethod,selectedSpecType]);
 
  useEffect(()=>{
  if(state.mic && state.mic.length>0)
  {
    setSelectedSpecimen(state.mic[0].specimenNumber);
    const item = state.patient.getTestData().find((v) => v.specimenNumber == state.mic[0].specimenNumber);
    if (item) {
     setSelectedTestMethod(item.testMethod);
     setSelectedSpecType(item.sampleType);
     
   } else {
     setSelectedTestMethod('');
     setSelectedSpecType('');
     setBacteriaData(null);
   }
  }

 },[state.patient,state.mic]);

  useEffect(() => {
    const fetchBacteria = async () => {
      if (bacteriaData) {
        try {
          let bacteriaId = await handleBactery(bacteriaData.bacteria_name);
          setBacteriaId(bacteriaId);
        } catch (error) {
          console.error("Error fetching bacteria:", error);
          // Handle error state if needed
        }
      }
    };
  
    fetchBacteria();
  }, [bacteriaData]);

  useEffect(() => {
    if (resultContainerRef.current) {
     
      resultContainerRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'end',
      });
    }
  }, [results]); 

  const handleClose = () => {
    setDialogVisible(false);
  };

  const handleGenerateReport = () => {
    if (selectedSpecimen && selectedAuthorities.length > 0) {
      onGenerateReport(selectedSpecimen, selectedAuthorities);
    }
  };

  const onGenerateReport = (specimenNumber, authority) => {
    

    if (!specimenNumber || !authority) {
      setHintMessage("选择标本编号或权威机构");
      setDialogVisible(true);
      return;
    }
    if (!results || results.length === 0) {
      setHintMessage("没有可生成的报告数据");
      setDialogVisible(true);
      return;
    }
  
    let Mic = [];
  
    results.slice().reverse().forEach((result, index) => {
      let ecv="";
  
      if (!result.breaking || result.breaking.length === 0) {
        console.warn(`Skipping result at index ${index} due to missing breaking data.`);
        return;
      }
  
      // Filter breaking data based on authority
      const filteredBreaking = result.breaking.find((record) =>
        record.comments?.includes(authority)
      );
          
  
      // Select breaking data with fallback logic
      const selectedBreaking =
        filteredBreaking ||
        result.breaking.find((record) => {
          const micValues = [
            record?.["S (μg/mL)"] ?? "",
            record?.["R (μg/mL)"] ?? "",
            record?.["I (μg/mL)"] ?? "",
          ].filter((value) => value !== "" && value !== "-");
        
          // Define values for Disk method
          const diskValues = [
            record?.["S (mm)"] ?? "",
            record?.["R (mm)"] ?? "",
            record?.["I (mm)"] ?? "",
          ].filter((value) => value !== "" && value !== "-");
          if (result.selectedMethod === "MIC") { 
           
            return micValues.length >= 2; // At least two non-empty values for MIC
          } else if (result.selectedMethod === "Disk") {
            return diskValues.length >= 2; // At least two non-empty values for Disk
          } else if (result.selectedMethod === "MIC+Disk") {
            return micValues.length >= 1 && diskValues.length >= 1; // At least one non-empty value for both
          } 
          return false; // At least two non-empty values
        }) || result.breaking[0]; // Fallback to the first record
  
      // Extract breaking data values
      if(result.selectedMethod==="MIC"){
        const sValue = selectedBreaking?.["S (μg/mL)"] ?? "";
        const rValue = selectedBreaking?.["R (μg/mL)"] ?? "";
        const iValue = selectedBreaking?.["I (μg/mL)"] ?? "";
        const rOrIValue = rValue || iValue;
        ecv=`${sValue}${rOrIValue ? `,${rOrIValue}` : ""}`;
      }
      if (result.selectedMethod === "Disk") {
        const s_mmValue = selectedBreaking?.["S (mm)"] ?? "";
        const r_mmValue = selectedBreaking?.["R (mm)"] ?? "";
        const i_mmValue = selectedBreaking?.["I (mm)"] ?? "";
        const rOrI_mmValue = r_mmValue || i_mmValue;
        ecv = `${s_mmValue}${rOrI_mmValue ? `,${rOrI_mmValue}` : ""}`;
      }
      if (result.selectedMethod === "MIC+Disk") {
        const sValue = selectedBreaking?.["S (μg/mL)"] ?? "";
        const rValue = selectedBreaking?.["R (μg/mL)"] ?? "";
        const s_mmValue = selectedBreaking?.["S (mm)"] ?? "";
        const r_mmValue = selectedBreaking?.["R (mm)"] ?? "";
        
        const micPart = `${sValue}${rValue ? `,${rValue}` : ""}`;
        const diskPart = `${s_mmValue}${r_mmValue ? `,${r_mmValue}` : ""}`;
        
        // Combine MIC and Disk ECV parts
        ecv = `${micPart}${micPart && diskPart ? ";" : ""}${diskPart}`;
      }
  
      // Combine unique comments with `\n` separator
      const uniqueComments = [
        ...new Set(
          result.breaking
            .map((record) => record.comments)
            .filter((comment) => {
              if (authority.length === 1 && authority[0] === 'CLSI') {
                // Include all comments when authority is ['CLSI']
                return comment;
              }
              // Otherwise, check if comment matches any authority
              return comment && authority.some((auth) => comment.includes(auth));
            })
        ),
      ].join("\n");
      
     
  
      // Combine comments with remarks
      const combinedRemark = `${result.remark || ""}\n${uniqueComments}`.trim();
  
      // Create MICData instance
      const micData = new MICData();
      micData.Agent = result.antimicrobialAgent ?? "未知药物";
      micData.ECV = ecv;
      micData.Bacteria = bacteriaData.bacteria_name ?? "未知细菌";
      micData.MIC = result.micValue ?? "未知MIC值";
      micData.DISK = result.diskContent ?? "未知disk值";
      micData.SORT = selectedBreaking?.sensitivity ?? "未知敏感性分类";
      micData.Remark = combinedRemark;
      micData.specimenNumber = specimenNumber || "未指定标本编号";
      micData.selectedMethod=result.selectedMethod;
      // Add to Mic array
      Mic.push(micData);
    });
  
    if (Mic.length === 0) {
      setHintMessage("报告生成失败，数据不足");
      setDialogVisible(true);
      return;
    }
  
    // Dispatch the generated report
    dispatch({ type: "SET_MIC", payload: Mic });
    setHintMessage("报告已生成,可在'试验报告'项目中查看");
    setDialogVisible(true);
  };


  const handleSubmit = async () => {
    try {
      const updatedAntibiotics = [...bacteriaData.antibiotics];
    
      // 过滤掉没有 diskContent 和 mic 的抗菌药物项
      const filteredAntibiotics = updatedAntibiotics.filter(antibiotic => antibiotic.diskContent || antibiotic.mic);
     
      for (let i = 0; i < filteredAntibiotics.length; i++) {
        const antibiotic = filteredAntibiotics[i];
       
        const antimicrobialAgent= antibiotic.name;
        let selectedMethod="MIC"
        let micValue = "";
        let diskContent = "";
    
        // 判断选择的测试方法（Disk + MIC，Disk，MIC）
        if (antibiotic.mic && antibiotic.diskContent) {
          selectedMethod = "DISK+MIC";
          micValue = antibiotic.mic;
          diskContent = antibiotic.diskContent;
        } else if (antibiotic.diskContent) {
          selectedMethod = "Disk";
          diskContent = antibiotic.diskContent;
        } else if (antibiotic.mic) {
          selectedMethod = "MIC";
          micValue = antibiotic.mic;
        }
    
        // 创建请求数据
        const data = {
          bacteriaId,
          antimicrobialAgent,
          micValue,
          diskContent,
          selectedMethod
        };
      
        await doSearch(data);  // 等待请求完成
      }

      const clearedAntibiotics = updatedAntibiotics.map((antibiotic) => {
        // If mic or diskContent is set, reset it to empty string
        if (antibiotic.mic || antibiotic.diskContent) {
          return {
            ...antibiotic,
            mic: '',
            diskContent: ''
          };
        }
        return antibiotic;  // Keep the rest of the antibiotic data intact
      });
  
      // Update state with cleared antibiotics
      setBacteriaData(prevState => ({
        ...prevState,
        antibiotics: clearedAntibiotics
      }));
    
    } catch (error) {
      setError('请求失败，请稍后再试');
      console.error('Search error:', error);
    }
    };

    const deleteMicInfo= async()=>{
      setResults([]);
      
    };

  const handleBactery = async (bacterName) => {
    try {
        const encodedName = encodeURIComponent(bacterName);
        const response = await fetch(`${BASE_URL}api/suggestions?query=${encodedName}`);
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        const data = await response.json();
        return data && data.length > 0 ? data[0].bacteriaId : null;
        
    } catch (error) {
        console.error('Error fetching suggestions:', error);
        return null; // explicit return in catch block
    }
};

  const getResultClass = (result) => {
    return `bacter_result ${result === "I" ? 'bacter_resistant' : 'bacter_sensitive'}`;
  };



  const fetchBacteriaData= async ()=>{
    const data = { selectedTestMethod,selectedSpecType };
    try {
      const response = await fetch(`${BASE_URL}api/bacteria/interpreter`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (response.ok) {
        const resultData = await response.json();
       
        const filteredAntibiotics = resultData.antibiotics.filter(antibiotic => 
          antibiotic && (antibiotic.result === "S" || antibiotic.result === "I")
        );
        
            
         setBacteriaData({ ...resultData, antibiotics: filteredAntibiotics });
         setError(''); 
        }   
     else{
          setError('系统没有发现可用的MIC,请转到医药词典->新建Mic值');
          return;
        }
      
      
    } catch (error) {
      console.log(error)
    }
  };

  const handleNoChanged = (e) => {
    const selectedSpecimenNumber = e.target.value;
     setSelectedSpecimen(selectedSpecimenNumber);
     const item = state.patient.getTestData().find((v) => v.specimenNumber == selectedSpecimenNumber);
     if (item) {
      setSelectedTestMethod(item.testMethod);
      setSelectedSpecType(item.sampleType);
      
    } else {
      setSelectedTestMethod('');
      setSelectedSpecType('');
      setBacteriaData(null);
    }
  };
  const resetForm=()=>{
    setSelectedSpecType("");
    setSelectedSpecimen("");
    setSelectedTestMethod("");
    setBacteriaData(null);
   
    setError(""); 
  };

  const handleInputChange = (index, field, value) => {
    const updatedAntibiotics = [...bacteriaData.antibiotics];
    updatedAntibiotics[index][field] = value;
   
    setBacteriaData({
      ...bacteriaData,
      antibiotics: updatedAntibiotics
    });
   
  };


  const doSearch= async (data)=>{
    try {
     
      const response = await fetch(`${BASE_URL}api/multimethods/breaking`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });
  
      if (response.ok) {
        const resultData = await response.json();
       
        if(resultData && resultData.breaking){
          setResult(resultData);
          setResults(prevResults => [...prevResults, resultData]);
          setError(''); 
       
        }
        else{
          setError('系统没有发现可用的MIC,请转到医药词典->新建Mic值');
          return;
        }
      
      
      } else {
        setError('查询失败:No matching breaking data found');
        return;
       
      }
    } catch (error) {
      setError('系统没有发现可用的MIC,请转到医药词典->新建Mic值');
    }finally {
    
     
    }
  };

  if(!state.patient)
  return (
    <div className="report-container">
      <div className="patient-selection-content">
        <ShieldQuestion size={35} />
        <span className="patient-selection-text">还没有选择标本信息!!</span>
      </div>
    </div>
  );
  {error && (
    <div style={{ color: 'red', marginBottom: '10px', textAlign: 'center', fontSize: '16px' }}>
      {error}
    </div>
  )}

 

  return (
    <div className="bacter_container">
      <div className="bacter_header">
        <h1 className="bacter_title">细菌药敏试验用药指导</h1>
        <h6 style={{ textAlign: 'center', margin: 10, color: 'white', fontSize: '14px' }}> Mic 折点计算<br /> {state.patient ? '当前执行患者：' + state.patient.name : '还没有选择患者'}</h6>
     
        <select  className="agent-select"
         value={selectedSpecimen} 
        onChange={handleNoChanged}
         style={{ width: ` ${bacteriaData? '25%' : '100%'}`, height: "35px" }}>
           <option value="">请选择检查方法</option>
          {state.patient.getTestData().map((specimen) => (
            <option value={specimen.specimenNumber} >
              {specimen.testMethod}
            </option>
          ))}
        </select>
        <p className="bacter_subtitle">
      {selectedTestMethod && selectedSpecType 
        ? `标本来源：${selectedSpecType}; ` 
        : ''} 
      {bacteriaData ? `细菌: ${bacteriaData.bacteria_name}` : ''}
    </p>

    </div>
   
    
    {bacteriaData?(    <div className="bacter_table_container">
        <table className="bacter_table">
          <thead>
          <tr>
              <th className="bacter_header_main">抗菌药物</th>
              <th className="bacter_header_main">药评结果</th>
              <th className="bacter_header_main">纸片(μg)</th>
              <th className="bacter_header_main">MIC(mg/L)</th>
              <th className="bacter_header_main">抗菌药物</th>
              <th className="bacter_header_main">药评结果</th>
              <th className="bacter_header_main">纸片(μg)</th>
              <th className="bacter_header_main">MIC(mg/L)</th>
            </tr>
          </thead>
          <tbody>
          {(() => {
              const rows = [];
              const antibiotics = bacteriaData.antibiotics || [];
              for (let i = 0; i < antibiotics.length; i += 2) {
                rows.push(
                  <tr key={i}>
                    <td>{antibiotics[i]?.name || ''}</td>
                    <td>  <span className={getResultClass(antibiotics[i].result)}>
                      {antibiotics[i].result === "R" ? "耐药" : 
                      antibiotics[i].result === "S" ? "敏感" : 
                      antibiotics[i].result === "I" ? "中等" : ""}
                    </span></td>
                     <td>  <input
                    type="text"
                    value={antibiotics[i].diskContent}
                    onChange={(e) => handleInputChange(i, 'diskContent', e.target.value)}
                    className="bacteria_mic-input"
                  /></td>  
                    <td> <input
                    type="text"
                    value={antibiotics[i].mic}
                    onChange={(e) => handleInputChange(i, 'mic', e.target.value)}
                    className="bacteria_mic-input"
                  /></td> 
                  {i + 1 < antibiotics.length && (
                    <>
                    <td>{antibiotics[i + 1]?.name || ''}</td>
                    <td><span className={getResultClass(antibiotics[i+1].result)}>
                  {antibiotics[i+1].result === "R" ? "耐药" : 
                  antibiotics[i+1].result === "S" ? "敏感" : 
                  antibiotics[i+1].result === "I" ? "中等" : ""}
                </span></td>
                    <td>  <input
                    type="text"
                    value={antibiotics[i + 1]?.diskContent || ''}
                    onChange={(e) => handleInputChange(i + 1, 'diskContent', e.target.value)}
                    className="bacteria_mic-input"
                  /></td>  
                    <td> <input
                    type="text"
                    value={antibiotics[i + 1]?.mic || ''}
                    onChange={(e) => handleInputChange(i + 1, 'mic', e.target.value)}
                    className="bacteria_mic-input"
                  /></td></>  )}
                   
                  </tr>
                );
              }
              return rows;
            })()}
          </tbody>
          </table>
        </div>):("")}
     

      {bacteriaData?(  <div className="bacteria_submit-button-container">
            <button type="button" className="bacteria_submit-button" onClick={handleSubmit}>查询</button>
            <button type="button" className="bacteria_reset-button" onClick={resetForm}>重置</button>
          </div>):("")}

      {bacteriaData&& bacteriaData.interpretation?(<div>
          <h2 className="bacter_interpretation_title">解读说明：</h2>
          <div className="bacter_interpretation">
            <p className="bacter_interpretation_item">
                {bacteriaData.interpretation}
              </p>
         </div>
        </div>):("")}
        <div ref={resultContainerRef}>
        {results && results.length > 0 && (
       
       <table className="compound-table">
         <thead>
           <tr>
             <th rowSpan="2" className="first-col">抗菌药物</th>
             <th rowSpan="2">纸片含量<br />(μg)</th>
             <th colSpan="3" className="group-header">纸片扩散法<br />(mm)</th>
             <th colSpan="3" className="group-header">MIC<br />(μg/mL)</th>
             <th rowSpan="2" >解析</th>
           </tr>
          <tr className="sub-header">
             <th>S</th>
             <th>R</th>
             <th>DISK(值)</th>
             <th>S</th>
             <th>R</th>
             <th>MIC(值)</th>
           </tr>
         </thead>
         <tbody>
         {results.slice().reverse().map((result, index) => (
<React.Fragment key={index}>
 {/* Inner Loop: Render breaking records */}
 {result.breaking.map((breakRecord, breakIndex) => (
   <React.Fragment key={`${index}-${breakIndex}`}>
     {/* Main Data Row */}
     <tr>
       <td>{result.antimicrobialAgent}</td>
       <td>{breakRecord['Disk Content'] ? breakRecord['Disk Content'] : "-"}</td>
       <td>{breakRecord['S (mm)'] ? breakRecord['S (mm)'] : "-"}</td>
       <td>{breakRecord['R (mm)'] ? breakRecord['R (mm)'] : "-"}</td>
       <td>{result.diskContent ? result.diskContent : "-"}</td>
       <td>{breakRecord['S (μg/mL)'] ? breakRecord['S (μg/mL)'] : "-"}</td>
       <td>{breakRecord['R (μg/mL)'] ? breakRecord['R (μg/mL)'] : "-"}</td>
       <td>{result.micValue?result.micValue:"-"}</td>
       <td>{breakRecord.sensitivity?breakRecord.sensitivity:"-"}</td>
     </tr>

          {/* Comments Row */}
          {breakRecord.comments && (
            <tr>
              <td colSpan="10" className="text-left">
                <Lightbulb style={{ marginRight: '0.2rem' }} size={20} />
                <span>{breakRecord.comments}</span>
              </td>
            </tr>
          )}
        </React.Fragment>
      ))}

      {/* Optional Remark Section */}
      {result.remark && (
        <tr>
          <td colSpan="10" className="text-left">
            <BellRing style={{ marginRight: '0.2rem', color: 'red' }} size={20} />
            <span>{result.remark}</span>
          </td>
        </tr>
      )}
      {result.drugDeduction && (
      <tr>
        <td colSpan="10" className="text-left">
          <BellRing style={{ marginRight: '0.2rem', color: 'red' }} size={20}  />
          <span>{result.drugDeduction}</span>
        </td>
      </tr>
    )}

      </React.Fragment>
      ))}

      
         </tbody>
       </table>
    
     )}
     </div>

{dialogVisible && (
              <CustomDialog
                message={hintMessage}
                onClose={handleClose} />
            )}

{results && results.length > 0 && (
  
<div className="submit-button-container">
  {/* Clear Screen Button */}
    <button
      type="button"
      onClick={deleteMicInfo}
      className="delete-button"
    >
      清空屏幕
    </button>
</div>
 )}


{results && results.length > 0 && (
  
  <div style={{ display: "flex", alignItems: "center", gap: "20px", width:"100%" }}>
        <Checkbox.Group
          style={{ display: "flex", flexDirection: "row", gap: "10px" }}
          options={authorities.map(auth => ({
            label: <span style={{ color: "white" }}>{auth}</span>,
            value: auth
          }))}
          onChange={setSelectedAuthorities}
        />
        <Button type="primary" onClick={handleGenerateReport}>
          生成报告
        </Button>
      </div>
   )}
     </div>
    );
};

export default BacterialSensitivityResults;