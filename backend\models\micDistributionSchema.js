const mongoose = require('mongoose');

const micDistributionSchema = new mongoose.Schema({
  bacteria: { type: String, required: true },
  mechanism: { type: String, required: true },
  antibiotic: { type: String, required: true },
  min: { type: Number, required: true },
  max: { type: Number, required: true },
  distribution: [
    {
      mic: { type: Number, required: true },
      count: { type: Number, required: true },
      percent: { type: Number, required: true }
    }
  ]
}, { collection: 'micRange' });

module.exports = mongoose.model('MicDistribution', micDistributionSchema);
