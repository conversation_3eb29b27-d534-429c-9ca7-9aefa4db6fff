import React, { useState, useEffect } from 'react';
import { Table, Input, Button, Form, message,Select  } from 'antd';
import '../QueryForm.css'; 
const SpecManager = () => {
  const [form] = Form.useForm();  // 表单实例
  const [data, setData] = useState([]);  // 存储从API获取的数据
  const [loading, setLoading] = useState(false);  // 加载状态
  const BASE_URL = process.env.REACT_APP_BASE_URL;
  // 获取标本数据
  const fetchData = async () => {
    try {
      const response = await fetch(`${BASE_URL}api/tdictspec`);
      if (response.ok) {
        const data = await response.json();
        setData(data);
      } else {
        console.error('Failed to fetch entries');
      }
    } catch (error) {
      console.error('Error fetching entries:', error);
    }
  };

  useEffect(() => {
    fetchData();  // 页面加载时获取数据
  }, []);

  const handleReset = () => {
    form.resetFields();  // 清空所有输入框
  };

  const handleQuery = async () => {
    try {
        const values = form.getFieldsValue();
       
      const response = await fetch(`${BASE_URL}api/tdictspec/search?code=${values.code.toLowerCase()}`);
      if (response.ok) {
        const data = await response.json();
        const formData = {
          ...data,
         
        };
        form.setFieldsValue(formData);
        message.success('查询成功');
      } else {
        message.warning('未找到相关数据');
      }
    } catch (error) {
      console.error('Error searching by code:', error);
    }
  };
  



  // 表格列定义
  const columns = [
    {
      title: '标本代码',
      dataIndex: 'code',
      key: 'code',
    },
    {
      title: '中文名称',
      dataIndex: 'chinesename',
      key: 'chinesename',
    },
    {
      title: '英文名称',
      dataIndex: 'englishname',
      key: 'englishname',
    },
   

  ];

  return (
    <div  className="container">
      <Form form={form} layout="vertical">
        <Form.Item label={<span style={{ color: 'white' }}>标本代码</span>} name="code" rules={[{ required: true, message: '请输入标本代码' }]}>
          <Input placeholder="输入标本代码" className='mic-input' />
        </Form.Item>
        <Form.Item  label={<span style={{ color: 'white' }}>中文名称</span>} name="chinesename" rules={[{ required: true, message: '请输入中文名称' }]}>
          <Input placeholder="输入中文名称" className='mic-input' />
        </Form.Item>
        <Form.Item  label={<span style={{ color: 'white' }}>英文名称</span>} name="englishname">
          <Input placeholder="输入英文名称" className='mic-input'  />
        </Form.Item>
       

        <div style={{ display: 'flex', justifyContent: 'space-between', width:'80%' }}>
          <Button type="primary" onClick={handleQuery}>
            查询
          </Button>
          <Button onClick={handleReset} style={{ marginLeft: 8 }}>
            重置
          </Button>
        </div>
      </Form>

      <Table
        columns={columns}
        dataSource={data}
        rowKey="_id"
        loading={loading}
        scroll={{ x: 1000 }}  // 允许横向滚动，限制宽度
        style={{ marginTop: '20px',width:'80%' }}
      />
    </div>
  );
};

export default SpecManager;
