import React, { useEffect, useState } from 'react';
import { Tooltip } from 'antd';
import { FolderInput } from 'lucide-react';
import axios from 'axios';
import CustomDialog from './componets/CustomDialog';
import './QueryForm.css'

const BacteriaForm = () => {
const [testMethod, setTestMethod] = useState('');
const [bacteriaName, setBacteriaName] = useState('');
const [specType, setSpecType] = useState('');
const [antibiotics, setAntibiotics] = useState([{ name: '', result: '' }]);
const [interpretation, setInterpretation] = useState('');
const [interpretation_name, setInterpretation_Name] = useState('');
const [dialogVisible, setDialogVisible] = useState(false);
const [bacteriaList, setBacteriaList] = useState([]); 
const [hintMessage,setHintMessage] = useState('');
const [selectedSampleId, setSelectedSampleId] = useState(null);
const [error, setError] = useState('');
const BASE_URL = process.env.REACT_APP_BASE_URL;
useEffect(() => {
    fetchBacteriaData();
  }, []);

  const fetchBacteriaData = async () => {
    try {
      const response = await axios.get(`${BASE_URL}api/bacteria`);
      setBacteriaList(response.data);
    } catch (error) {
      setError('Failed to fetch bacteria data');
      console.error('Error fetching data:', error);
    }
  };
  const fetchSampleDetails = async (sampleId) => {
    try {
      if(!sampleId) return;
      const response = await axios.get(`${BASE_URL}api/bacteria/detail/${sampleId}`);
       const sampleDetails = response.data;
       setTestMethod(sampleDetails.testMethod);
       setSpecType(sampleDetails.specType);
       setBacteriaName(sampleDetails.bacteria_name);
       setInterpretation_Name(sampleDetails.interpretation_name);
       setAntibiotics(sampleDetails.antibiotics);
       setInterpretation(sampleDetails.interpretation);
       setSelectedSampleId(sampleId);
    } catch (error) {
      console.error('Error fetching sample details:', error);
    }
  };
  
const deleteReportInfo = async () => {
    if (!selectedSampleId) {
      setHintMessage('Please select a sample to delete');
      setDialogVisible(true);
      return;
    }

    try {
      const response = await axios.delete(`${BASE_URL}api/inter/${selectedSampleId}`);
      setBacteriaList(bacteriaList.filter(sample => sample._id !== selectedSampleId)); // Remove deleted sample from state
      setSelectedSampleId(null); // Reset selection
      resetForm();
      await fetchBacteriaData();
      setHintMessage('Sample deleted successfully');
      setDialogVisible(true);

    } catch (error) {
      console.error('Error deleting sample:', error);
      setHintMessage('Failed to delete sample');
      setDialogVisible(true)
    }
  };

const handleAntibioticChange = (index, field, value) => {
    const newAntibiotics = [...antibiotics];
    newAntibiotics[index][field] = value;
    setAntibiotics(newAntibiotics);
};

const addAntibiotic = () => {
    setAntibiotics([...antibiotics, { name: '', result: '' }]);
};
 
const handleFetch = async () => {
  try {
    if (!specType.trim()) {
       setError('Please enter a code.');
       return;
    }
       
    const response = await fetch(`${BASE_URL}api/spec/${specType.toLowerCase()}`); 
    if (!response.ok) {
      const errorData = await response.json();
      setError(errorData.message || 'Failed to fetch data.');
      return;
    }
    const data = await response.json();
    setSpecType(data.chineseName);
    setError('');
   
  } catch (err) {
    console.error('Error fetching data:', err);
    setError('An unexpected error occurred.');
  }
};

const handleSubmit = async (e) => {
    e.preventDefault();
    
    try {
        const response = await axios.post(`${BASE_URL}api/bacteria`, {
            interpretation_name:interpretation_name,
             bacteria_name: bacteriaName,
             specType:specType,
            antibiotics: antibiotics,
            interpretation: interpretation
        });
        setHintMessage('Data saved successfully!');
        setDialogVisible(true)
        resetForm();
        await fetchBacteriaData();
    } catch (error) {
        console.error('Error saving data:', error);
        setHintMessage('Failed to save data');
        setDialogVisible(true);
    }
};
//183
const fetchHstoryInf = async () => {
  try {
    const response = await fetch(`${BASE_URL}api/bacteria`);
    
    if (response.ok) {
     
      const fetchedData = await response.json();
      
      const sampleDetails = fetchedData[0];
       setTestMethod('');
       setSpecType(sampleDetails.spec_type);
       setBacteriaName(sampleDetails.bacteria_name);
       setAntibiotics(sampleDetails.antibiotics);
       setInterpretation('');
    
    } else {
      // 如果未找到患者信息，显示提示
      setHintMessage("没有找到历史数据");
      setDialogVisible(true);
    }
  } catch (error) {
    // 如果请求失败，显示错误信息
   
    setHintMessage("获取历史失败，请重试");
    setDialogVisible(true);
  }
};


const handleClose = () => {
    setDialogVisible(false);
  };

const resetForm=()=>{
setTestMethod('');
 setBacteriaName('');
 setInterpretation_Name('');
 setSpecType('');
 setInterpretation('');
 setSelectedSampleId('');
 setAntibiotics([]);
};

const handleRemoveAntibiotic = (index) => {
  const updatedAntibiotics = antibiotics.filter((_, i) => i !== index);
  setAntibiotics(updatedAntibiotics);
};


  return (
    <div className="container">
         <h6 style={{ textAlign: 'center', margin: 10, color: 'white',fontSize:'16px' }}>细菌典型报告解读</h6>
        
      <form className="query-form" onSubmit={handleSubmit}>

      <div className="form-group">
        <label className="form-label">推导名称:</label>
        <input
          type="text"
          name="interpretation_name" 
          value={interpretation_name}
          onChange={(e) => setInterpretation_Name(e.target.value)}
          required
          className="mic-input"
          placeholder="输入推导名称"
      
        />
      </div>
     

         <div className="form-group">
        <label className="form-label">细菌名称:</label>
        <input
          type="text"
          name="bacteriaName" 
          value={bacteriaName}
          onChange={(e) => setBacteriaName(e.target.value)}
          required
          className="mic-input"
          placeholder="输入适用的细菌名称"
      
        />
      </div>
        <div className="form-group">
        <label className="form-label">标本来源:</label>
        <input
          type="text"
          name="specType" 
          value={specType}
          onChange={(e) => setSpecType(e.target.value)}
          required
          className="mic-input"
          placeholder="输入标本来源"
      
        />
      </div>
     

      {antibiotics.map((antibiotic, index) => (
                    <div key={index} className="form-group">
                        <label className="form-label">抗菌药物</label>
                        <input
                            type="text"
                            value={antibiotic.name}
                            onChange={(e) => handleAntibioticChange(index, 'name', e.target.value)}
                            required
                            className="mic-input"
                        />
                        <label className="form-label">结果</label>
                        <select
                            name="testResult"
                            value={antibiotic.result}
                            required
                            onChange={(e) => handleAntibioticChange(index, 'result', e.target.value)}
                            className="agent-select"
                            style={{ width: "25%", height: "35px" }}
                          >
                          <option value="">请选择</option>
                            <option value="S">敏感</option>
                            <option value="R">耐药</option>
                            <option value="I">中等</option>
                          </select>
                          {/* 移除按钮 */}
                          <button
                            type="button"
                            onClick={() => handleRemoveAntibiotic(index)}
                            className="remove-button"
                            style={{ marginLeft: '10px', color: 'red' }}
                          >
                            移除
                          </button>
                    </div>
                ))}
                 <div className="submit-button-container">
                 <button  className="reset-button" type="button" onClick={addAntibiotic}>添加抗菌药物</button>
                 </div>
              

                <div className="form-group">
                    <label className="form-label">解读</label>
                    <textarea
                        value={interpretation}
                        onChange={(e) => setInterpretation(e.target.value)}
                        className="mic-input"
                        style={{ width: "80%", height: "85px",fontFamily:"Arial" }}
                        rows="12"
                        required
                    ></textarea>
                </div>
                <div className="submit-button-container">
                <button className="submit-button" type="submit">提交</button>
                <button type="button" className="reset-button" onClick={resetForm}>重置</button>
                </div>
               
      </form>

      {dialogVisible && (
        <CustomDialog
          message={hintMessage}
          onClose={handleClose}
        />
      )}

      {/* 显示样本数据 */}
      <div className="result-table-container" style={{ marginTop: '20px' }}>
        <h6 style={{ textAlign: 'center', margin: 10,  color: 'white' }}>药敏推导记录</h6>
        {bacteriaList.length > 0 ? (
          <><table className="result-table">
            <thead>
              <tr>
                <th>细菌名称</th>  
              
              </tr>
            </thead>
            <tbody>
            
              {bacteriaList
                .slice()
                .sort((a, b) => new Date(b.create_date) - new Date(a.create_date)) // Sort by create_date descending
                .map((sample, index) => (
                    <tr key={index}>
                    <td><span
                        onClick={(e) => {
                          e.stopPropagation(); // Prevent triggering row click event
                          fetchSampleDetails(sample._id);
                        }}
                        style={{ cursor: 'pointer', color: 'white' }}
                      >
                        {sample.interpretation_name} {/* Click to fetch details */}
                      </span></td>
                   
                  
                  </tr>
                ))}
            </tbody>
            

          </table>
          {selectedSampleId?(<div className="submit-button-container">
              <button
                type="button"
                onClick={deleteReportInfo}
                className="reset-button"
              >
                删除信息
              </button>
            </div>):("")}
       </>
          
        ) : (
          <p style={{ color: 'red', marginBottom: '10px', textAlign:'center', fontSize:'16px' }}>{error || '暂无药敏推导记录'}</p>
        )}


      </div>

    </div>
  );
};

export default BacteriaForm;
