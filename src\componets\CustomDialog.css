/* CustomDialog.css */
.custom-dialog-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
  }
  
  .custom-dialog {
    background-color: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
    max-width: 300px;
    text-align: center;
  }

  .custom-dialog p {
    margin: 0; /* Ensure no margin collapses the text */
    color: #000; /* Ensure text color is set */
    font-size: 16px; /* Ensure font size is appropriate */
  }
  
  .custom-dialog-buttons {
    margin-top: 20px;
    display: flex;
    justify-content: center;
  }
  
  .custom-dialog-buttons button {
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    background-color: #007bff;
    color: #fff;
    cursor: pointer;
    font-size: 16px;
  }
  
  .custom-dialog-buttons button:hover {
    background-color: #0056b3;
  }
  