// services/taxonomyService.js
import axios from 'axios';
const BASE_URL = process.env.REACT_APP_BASE_URL;
const API_URL = 'api/taxonomy';

// 获取树形结构
export const fetchTaxonomyTree = async () => {
  const response = await axios.get(`${BASE_URL}${API_URL}/tree`);
  return response.data;
};

// 获取父节点选项
export const fetchParentNodes = async (nodeId) => {
  try {
   
    const response = await axios.get(`${BASE_URL}${API_URL}/taxonomy/parent/${nodeId}`);
    return response.data;
  } catch (error) {
    console.error("Error fetching parent nodes:", error);
    return null; 
  }
};

export const updateTaxonomyNode = async (key, name) => {
  try {
    const response = await fetch(`${BASE_URL}api/taxonomy/update`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ key, name }),
    });

    if (!response.ok) {
      throw new Error('Failed to update taxonomy node.');
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error updating taxonomy node:', error);
    throw error;
  }
};


// 添加或编辑节点
export const addOrEditNode = async (data) => {

  if (data._id) {
    // 编辑模式
    await axios.put(`${BASE_URL}${API_URL}/updateNode/${data._id}`, data);
  } else {
    // 添加模式
   
    await axios.post(`${BASE_URL}${API_URL}/addNode`, data);

  }
};

// 删除节点
export const deleteTaxonomyNode = async (id) => {
  await axios.delete(`${BASE_URL}${API_URL}/deleteNode/${id}`);
};
