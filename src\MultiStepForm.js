import React, { useEffect, useState } from 'react';
import { Form, Input, Select, DatePicker, Button, Steps, Card, Table, message } from 'antd';
import SpecimenData from './models/SpecimenData';
import SpecimenModal from "./componets/ui/SpecimenModal";
import CustomDialog from './componets/CustomDialog';
import { useSharedState } from './SharedStateContext';
import { Trash2 } from "lucide-react";
import TestData from './models/TestData';
import TestDataModal from './componets/ui/TestDataModal';
import dayjs from 'dayjs';
const { Step } = Steps;

const MultiStepForm = ({ handleNavigation }) => {
  const [dialogVisible, setDialogVisible] = useState(false);
  const [hintMessage,setHintMessage] = useState('');
  const {state, dispatch } = useSharedState();
  const [currentStep, setCurrentStep] = useState(0);
  const [formData, setFormData] = useState(new SpecimenData({}));
  const [testMethod,setTestMethod] = useState('');
  const [testResult,setTestResult] = useState('');
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isResetModalVisible, setIsResetModalVisible] = useState(false);
 
  const [form] = Form.useForm();
  const BASE_URL = process.env.REACT_APP_BASE_URL;


  useEffect(() => { 
    form.setFieldsValue(formData);
  }, [currentStep]);
  
  useEffect(()=>{
    
  if(testResult && testMethod){
     addNewTestData(testMethod,testResult)
   
  }
  },[testResult])

  useEffect(() => {
    if (state.patient) {
      setFormData((prevData) => {
        const updatedData = prevData instanceof SpecimenData ? prevData : new SpecimenData(prevData);
  
        // Merge state.patient without overwriting existing testDataList
        Object.assign(updatedData, state.patient);
  
        console.log("Updated formData from state:", updatedData);
        return updatedData;
      });
    }
  }, [state.patient]);
  

  // 表单配置数据
  const formSections = [
    {
      title: '患者信息',
      fields: [
     
 
        { name: 'PATIENT_ID', label: '患者ID', type: 'input'},
        { name: 'FULL_NAME', label: '姓名', type: 'input' },
        { name: 'SEX', label: '性别', type: 'select', options: [
          { value: '1', label: '男' },
          { value: '0', label: '女' },
         
        ]},

        { name: 'AGE', label: '年龄', type: 'input' },
        { name: 'PHONE', label: '联系电话', type: 'number' },
        { name: 'ADDRESS', label: '住址', type: 'input' },
       
       
        { name: 'ID_NO', label: '病历号', type: 'input' },
      ]
    },
    {
      title: '医院信息',
      fields: [
        { name: 'HOSPITAL_NO', label: '医院编号', type: 'input' },
 
        { name: 'WARD', label: '病房',  type: 'select', options: [
            { value: '', label: '请选择' },
            { value: 'in', label: '住院' },
            { value: 'out', label: '门诊' },
            { value: 'eme', label: '急诊' },
            { value: 'icu', label: 'ICU' },
        ]},
        {
          name: 'WARD_TYPE',
          label: '病房类型',
          type: 'select',
          options: [
            { value: '', label: '请选择' },
            { value: 'single', label: '单人病房' },
            { value: 'double', label: '双人病房' },
            { value: 'multi', label: '多人病房' },
            { value: 'deluxe', label: '豪华病房' },
            { value: 'vip', label: 'VIP病房' },
            { value: 'isolation', label: '隔离病房' },
            { value: 'icu', label: '重症监护病房(ICU)' },
            { value: 'nicu', label: '新生儿重症监护病房(NICU)' },
            { value: 'picu', label: '儿科重症监护病房(PICU)' },
            { value: 'ccu', label: '心脏重症监护病房(CCU)' },
            { value: 'emergency', label: '急诊观察病房' }
          ]
        },


        { name: 'INSTITUT', label: '机构', type: 'select', options: [
          { value: '', label: '请选择' },
          { value: 'frist', label: '一级' },
          { value: 'second', label: '二级' },
          { value: 'third', label: '三甲' },
      
      ]},
      {
        name: 'DEPARTMENT',
        label: '科室',
        type: 'select',
        options: [
          { value: '', label: '请选择' },
          { value: '心血管内科', label: '心血管内科' },
          { value: '呼吸内科', label: '呼吸内科' },
          { value: '消化内科', label: '消化内科' },
          { value: '内分泌科', label: '内分泌科' },
          { value: '肾内科', label: '肾内科' },
          { value: '血液科', label: '血液科' },
          { value: '神经内科', label: '神经内科' },
          { value: '风湿免疫科', label: '风湿免疫科' },
          { value: '普通外科', label: '普通外科' },
          { value: '骨科', label: '骨科' },
          { value: '心胸外科', label: '心胸外科' },
          { value: '神经外科', label: '神经外科' },
          { value: '泌尿外科', label: '泌尿外科' },
          { value: '整形外科', label: '整形外科' },
          { value: '妇科', label: '妇科' },
          { value: '产科', label: '产科' },
          { value: '计划生育科', label: '计划生育科' },
          { value: '新生儿科', label: '新生儿科' },
          { value: '小儿内科', label: '小儿内科' },
          { value: '小儿外科', label: '小儿外科' },
          { value: '急诊科', label: '急诊科' },
          { value: '重症医学科(ICU)', label: '重症医学科(ICU)' },
          { value: '麻醉科', label: '麻醉科' },
          { value: '放射科', label: '放射科' },
          { value: '超声科', label: '超声科' },
          { value: '核医学科', label: '核医学科' },
          { value: '检验科', label: '检验科' },
          { value: '病理科', label: '病理科' },
          { value: '眼科', label: '眼科' },
          { value: '耳鼻喉科', label: '耳鼻喉科' },
          { value: '皮肤科', label: '皮肤科' },
          { value: '口腔科', label: '口腔科' },
          { value: '中医科', label: '中医科' },
          { value: '康复科', label: '康复科' },
          { value: '肿瘤科', label: '肿瘤科' },
          { value: '感染科', label: '感染科' },
          { value: '心理科', label: '心理科' },
          { value: '营养科', label: '营养科' }
        ]
      },
      {
        name: 'LAB',
        label: '化验室',
        type: 'select',
        options: [
          { value: '', label: '请选择' },
          { value: 'clinical', label: '临床检验科' },
          { value: 'pathology', label: '病理科' },
          { value: 'blood', label: '输血科' },
          { value: 'molecular', label: '分子诊断实验室' },
          { value: 'endocrine', label: '内分泌检验室' },
          { value: 'drug', label: '药物浓度监测实验室' },
          { value: 'special', label: '特殊检验室' },
          { value: 'research', label: '科研实验室' },
          { value: 'emergency', label: '急诊检验室' },
          { value: 'central', label: '中心实验室' },
          { value: 'genetics', label: '遗传与产前诊断实验室' },
          { value: 'toxicology', label: '毒物检测实验室' },
          { value: 'flow', label: '流式细胞实验室' },
          { value: 'mass', label: '质谱分析实验室' },
          { value: 'microbiology', label: '微生物与感染实验室' },
          { value: 'bloodBank', label: '血库' },
          { value: 'cellTherapy', label: '细胞治疗实验室' },
          { value: 'biobank', label: '生物样本库' }
        ]
      },
     
      
      ]
    },
    {
      title: '标本信息',
      fields: [

        { name: 'SPEC_NUM', label: '标本号', type: 'input' },
        { name: 'SPEC_DATE', label: '标本日期', type: 'date' },
        {
          name: 'SPEC_TYPE',
          label: '标本类型',
          type: 'select',
          options: [
            { value: '', label: '请选择' },
            { value: 'ur', label: '尿' },
            { value: 'bl', label: '血' },
            { value: 'ser', label: '血清' },
            { value: 'pla', label: '血浆' },
            { value: 'csf', label: '脑脊液' },
            { value: 'st', label: '粪便' },
            { value: 'sput', label: '痰' },
            { value: 'tiss', label: '组织' },
            { value: 'swab', label: '拭子' },
            { value: 'bile', label: '胆汁' },
            { value: 'per', label: '胸腹水' },
            { value: 'syn', label: '关节液' },
            { value: 'nail', label: '指甲' },
            { value: 'hair', label: '头发' },
            { value: 'semen', label: '精液' },
            { value: 'vag', label: '阴道分泌物' },
            { value: 'ear', label: '耳分泌物' },
            { value: 'nas', label: '鼻分泌物' },
            { value: 'wound', label: '伤口分泌物' }
          ]
        },
      
        { name: 'SMEAR_RESULT', label: '涂片结果', type: 'input' },
        { name: 'CULTURE_RESULT', label: '培养结果', type: 'input' },
       { name: 'COMMENT', label: '备注', type: 'textarea' },
      ]
    },
    {
      title: '药敏信息',
      fields: [

        { name: 'ESBL', label: 'ESBL', type: 'select', options: [
          { value: 'yes', label: '是' },
          { value: 'no', label: '否' }
        ]},
        { name: 'BETA_LACT', label: 'β-内酰胺酶', type: 'select', options: [
          { value: 'yes', label: '是' },
          { value: 'no', label: '否' }
        ]},
        { name: 'INDUC_CLI', label: '诱导性克林霉素', type: 'select', options: [
          { value: 'yes', label: '是' },
          { value: 'no', label: '否' }
        ]},
        { name: 'CARBAPENEM', label: '碳青霉烯类', type: 'select', options: [
          { value: 'yes', label: '是' },
          { value: 'no', label: '否' }
        ]},
      ]
    },

    {
      title: '检测信息',
      fields: [
      
        {
          name: 'TEST_METHOD',
          label: '检测方法',
          type: 'select',
           options: [
              { value: '', label: '请选择' },
              { value: 'EDTA-碳青霉烯灭活试验(eCIM)', label: 'EDTA-碳青霉烯灭活试验(eCIM)' },
              { value: '改良碳青霉烯灭活试验(mCIM)', label: '改良碳青霉烯灭活试验(mCIM)' },
              { value: '丝氨酸型碳青霉烯酶*', label: '丝氨酸型碳青霉烯酶*' },
              { value: '肺炎克雷伯菌、产酸克雷伯菌、大肠埃希菌和奇异变形杆菌超广谱β-内酰胺酶试验', label: '肺炎克雷伯菌、产酸克雷伯菌、大肠埃希菌和奇异变形杆菌超广谱β-内酰胺酶试验' },
              { value: '肠杆菌目细菌和铜绿假单胞菌碳青霉烯酶试验', label: '肠杆菌目细菌和铜绿假单胞菌碳青霉烯酶试验' },
              { value: '疑似产碳青霉烯酶肠杆菌目细菌和铜绿假单胞菌CarbaNP试验', label: '疑似产碳青霉烯酶肠杆菌目细菌和铜绿假单胞菌CarbaNP试验' },
              { value: '疑似产碳青霉烯酶肠杆菌目细菌和铜绿假单胞菌改良碳青霉烯灭活试验', label: '疑似产碳青霉烯酶肠杆菌目细菌和铜绿假单胞菌改良碳青霉烯灭活试验' },
              { value: '氨曲南＋头孢他啶-阿维巴坦肉汤纸片洗脱法', label: '氨曲南＋头孢他啶-阿维巴坦肉汤纸片洗脱法' },
              { value: '肠杆菌目细菌和铜绿假单胞菌黏菌素耐药性试验', label: '肠杆菌目细菌和铜绿假单胞菌黏菌素耐药性试验' },
              { value: '阳性血培养肉汤直接纸片扩散试验', label: '阳性血培养肉汤直接纸片扩散试验' },
              { value: '肠杆菌目细菌阳性血培养肉汤直接纸片扩散试验', label: '肠杆菌目细菌阳性血培养肉汤直接纸片扩散试验' },
              { value: '铜绿假单胞菌阳性血培养肉汤直接纸片扩散试验', label: '铜绿假单胞菌阳性血培养肉汤直接纸片扩散试验' },
              { value: '不动杆菌属阳性血培养肉汤直接纸片扩散试验', label: '不动杆菌属阳性血培养肉汤直接纸片扩散试验' },
              { value: '葡萄球菌属β-内酰胺酶检测试验', label: '葡萄球菌属β-内酰胺酶检测试验' },
              { value: '苯唑西林盐琼脂试验', label: '苯唑西林盐琼脂试验' },
              { value: '金黄色葡萄球菌和肠球菌属万古霉素琼脂筛选试验', label: '金黄色葡萄球菌和肠球菌属万古霉素琼脂筛选试验' },
              { value: '葡萄球菌属、肺炎链球菌和β-溶血性链球菌群诱导克林霉素耐药检测试验', label: '葡萄球菌属、肺炎链球菌和β-溶血性链球菌群诱导克林霉素耐药检测试验' },
              { value: '金黄色葡萄球菌高水平莫匹罗星耐药检测试验', label: '金黄色葡萄球菌高水平莫匹罗星耐药检测试验' },
              { value: '肠球菌属高水平氨基糖苷耐药检测试验', label: '肠球菌属高水平氨基糖苷耐药检测试验' }
            ]
        },
        { name: 'TEST_RESULT', label: '检测结果', type: 'select', options: [
          { value:"阳性", label: '阳性' },
          { value:"阴性", label: '阴性' }
        ]},
      
      ]

    }
    

  ];

  const handleReset = () => {
    
    setFormData(new SpecimenData());
    form.resetFields();
     setTestMethod('');
    setTestResult('');
    
    dispatch({ type: 'SET_USER', payload: null });  
  };

  const showModal = () => {
   
    setIsResetModalVisible(true);
  };

  // 渲染表单项
  function renderField(field) {
    if (field.name === 'TEST_METHOD') {
     
      return (
        <Select
          options={field.options}
          value={testMethod}
          onChange={(e)=>setTestMethod(e)}
        />
      );
    }
    if (field.name === 'TEST_RESULT') {
      return (
        <Select
          options={field.options}
          value={testResult}
          onChange={(e) => setTestResult(e)}
        />
      );
    }
    switch (field.type) {
      case 'select':
         return (
          <Select options={field.options} />
        );
      // case 'date':
      //   return (
      //     <DatePicker style={{ width: '100%' }}  />
      //   );
      case 'number':
        return (
          <Input type="number" />
        );
      case 'textarea':
        return (
          <Input.TextArea rows={4} />
        );
      default:
        return (
          <Input />
        );
    }
  }

  const addNewTestData = (testMethod, testResult) => {
     const test = new TestData({"testMethod": testMethod, "testResult":testResult});
     if(formData.isTestMethodExist(test.testMethod))
       {
        setHintMessage("检测方法已经存在");
        setDialogVisible(true);
        return;
       }
     formData.addTestData(test)

  };
  
  
  
  

  const fetchPatientInfo = async (specmenNo) => {
    try {
      const response = await fetch(`${BASE_URL}specimen/${specmenNo}`);
  
      if (response.ok) {
        const data = await response.json(); 
        const formattedDate = dayjs(data.SPEC_DATE);
        form.setFieldsValue({
          SPEC_DATE: formattedDate, 
          ...data, 
        });
       setFormData(new SpecimenData(data)); 
  
      } else {
        setHintMessage("没有找到标本信息");
        setDialogVisible(true);
      }
    } catch (error) {
      setHintMessage("获取标本信息失败，请重试");
      setDialogVisible(true);
    }
  };
  
  


  const onFinish=async(values)=> {
    
    try {
      // 使用 POST 请求调用 saveOrUpdate API
      const response = await fetch(`${BASE_URL}specimen`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });
  
      if (response.ok) {
        const result = await response.json();
  
        // 分发保存到全局状态的操作
        dispatch({ type: 'SET_USER', payload: formData });
        handleNavigation('mic');
      } else {
        // 处理服务器错误
        setHintMessage("提交失败，请重试");
        setDialogVisible(true);
      }
    } catch (error) {
      // 处理网络错误或其他错误
      console.error('请求失败:', error);
      setHintMessage("网络错误，请稍后重试");
      setDialogVisible(true);
    }
  };
  
  const handleClose = () => {
    setDialogVisible(false);
  };

  const convertJsonToCsv = (jsonData) => {
    try {
      // Get all keys and values
      const headers = Object.keys(jsonData);
      const values = Object.values(jsonData);

      // Escape and wrap values in quotes if they contain commas or quotes
      const escapeValue = (value) => {
        if (value === null || value === undefined) return '';
        const stringValue = String(value);
        if (stringValue.includes(',') || stringValue.includes('"') || stringValue.includes('\n')) {
          return `"${stringValue.replace(/"/g, '""')}"`;
        }
        return stringValue;
      };

      // Create CSV rows
      const headerRow = headers.map(header => escapeValue(header)).join(',');
      const valueRow = values.map(value => escapeValue(value)).join(',');

      return headerRow + '\n' + valueRow;
    } catch (error) {
      throw new Error('Failed to convert JSON to CSV: ' + error.message);
    }
  };

  const handleShowModal = () => {
    setIsModalVisible(true);
  };
  const handleCloseModal = () => {
    setIsModalVisible(false);
  };
  const handleResetCloseModal = () => {
    setIsResetModalVisible(false);
  };

  const handleDelete = (index) => {
    formData.removeTestData(index); 
    const updatedPatient = Object.assign(new SpecimenData(),formData);
    setFormData(updatedPatient);
   };

  function downloadCsv(filename, csvContent) {
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
     const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
  

  // 下一步
  const next = () => {
    form.validateFields(formSections[currentStep].fields.map(f => f.name))
      .then(values => {
        setFormData(prevData => {
           const updatedData = prevData instanceof SpecimenData ? prevData : new SpecimenData(prevData);
           Object.assign(updatedData, values);
  
          return updatedData;
        });
  
        setCurrentStep(currentStep + 1); // Move to next step
       
      })
      .catch(error => {
        console.log('表单验证失败:', error);
      });
  };
  
  
  

  // 上一步
  function prev() {
    setCurrentStep(currentStep - 1);
  }

  return (
    <div style={{ maxWidth: 600, margin: '0 auto', padding: 24,backgroundColor:'#ffffff', borderRadius: '8px' }}>
    
      <Steps current={currentStep} style={{ marginBottom: 24 }}>
        {formSections.map(section => (
          <Step key={section.title} title={section.title} />
        ))}
      </Steps>
      

      <Card title={formSections[currentStep].title}  >
        <Form
          form={form}
          layout="horizontal"
          labelCol={{ span: 6 }}
          wrapperCol={{ span: 18 }}
          initialValues={{ ...formData }} 
          onFinish={onFinish}
        >
      {formSections[currentStep].fields.map(field =>
        field.name === "SPEC_DATE" ? (
        <Form.Item
          key="SPEC_DATE"
          name="SPEC_DATE"
          label="取样日期"
          style={{ marginBottom: '10px' }}
          rules={[{ required: true, message: "请选择标本日期!" }]}
          getValueProps={(value) => ({ value: value ? dayjs(value) : null })}
        >
          <DatePicker style={{ width: '100%' }}  />
        </Form.Item>
  ) : (
    <Form.Item
      key={field.name}
      name={field.name}
      label={field.label}
      style={{ marginBottom: '10px' }}
      rules={[
        {
          required: currentStep !== 4,
          message: `请输入${field.label}`
        }
      ]}
    >
      {renderField(field)}
    </Form.Item>
  )
)}
    <TestDataModal
        visible={isModalVisible}
        onClose={handleCloseModal}
        testDataList={formData.getTestData()}
        onDelete={handleDelete}
       
      />
    <SpecimenModal
        visible={isResetModalVisible}
        onClose={handleResetCloseModal}
        fetchPatientInfo={fetchPatientInfo}
        handleReset={handleReset}
       
      />

          <Form.Item>
            <div style={{ display: 'flex', justifyContent: 'space-between', marginTop: 24 ,width:'100%'}}>
              {currentStep > 0 && (
                <Button style={{ marginRight: 8 }} onClick={prev}>
                  上一步
                </Button>
              )}
              {currentStep < formSections.length - 1 && (
                <Button type="primary" style={{ marginLeft: 'auto' }} onClick={next}>
                  下一步
                </Button>
              )}
         {currentStep === formSections.length - 1 && (
  <>
            <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'stretch' }}>
          {/* Button Container (Aligns Both Buttons) */}
          <div style={{ 
            width: '100%', 
            display: 'flex', 
            justifyContent: 'space-between',  // Distributes buttons evenly
            alignItems: 'center', 
            marginBottom: '16px' 
          }}>
            {/* Centered "查看" Button */}
            <Button type="link"  onClick={handleShowModal} style={{ margin: '0 auto' }}>详情</Button>

            {/* Right-Aligned "完成" Button */}
            <Button type="primary" onClick={() => form.submit()}>完成</Button>
          </div>
        </div>

  </>
)}
              {currentStep === 0 && (
                <>
                <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
                <Button type="link" onClick={showModal}>导入/重置标本</Button>
               
               </div>
                </>
              )}
            </div>
          </Form.Item>
          {dialogVisible && (
          <CustomDialog
            message={hintMessage}
            onClose={handleClose}
          />
        )}

      
        
        
        </Form>
      </Card>
    </div>
  );
};

export default MultiStepForm;



