import TestData from './TestData';

class Patient {
  constructor(
    patient_id = '',
    hospital_code = '',
    name = '',
    gender = '男',
    age = '',
    department = '',
    ward = '',
    bed = '',
    medicalRecordNumber = '',
    specimenNumber = '',
    consumableboardNumber = '',
    sampleType = '',
    sampleCode = '',
    bacteriaCount = '',
    cultureResult  = '',
    smearResult = '',
    remark = '',
    testDataList = []
  ) {
    this.patient_id = patient_id; // 患者ID
    this.hospital_code = hospital_code; // 医院代码
    this.name = name; // 姓名
    this.gender = gender; // 性别
    this.age = age; // 年龄
    this.department = department; // 科室
    this.ward = ward; // 病房
    this.bed = bed; // 病床号
    this.medicalRecordNumber = medicalRecordNumber; // 病历号
    this.specimenNumber = specimenNumber; // 病历号
    this.consumableboardNumber = consumableboardNumber; // 病历号
    this.sampleType = sampleType; // 病历号
    this.sampleCode = sampleCode; // 病历号
    this.bacteriaCount = bacteriaCount; // 病历号
    this.cultureResult = cultureResult; // 字符串，表示培养结果
    this.smearResult = smearResult; // 字符串，表示培养结果
    this.remark = remark; // 备注
    this.testDataList = testDataList; // TestData 数组
    this.bacteriaCode = ''; // 新增属性：细菌编码
  }


  // 添加一条 TestData 数据
  addTestData(testData) {
    if (testData instanceof TestData) {
        this.testDataList.push(testData);
    } else {
      throw new Error("testData 必须是 TestData 类的实例");
    }
  }

  isTestMethodExist(testMethod) {
    return this.testDataList.some(item => item.testMethod === testMethod);
  }
  

  // 删除指定索引的 TestData 数据
  removeTestData(index) {
    if (index >= 0 && index < this.testDataList.length) {
      this.testDataList.splice(index, 1);
    } else {
      throw new Error("索引无效");
    }
  }

  // 获取所有 TestData 数据
  getTestData() {
    return this.testDataList;
  }


  // 清空所有 TestData 数据
  clearTestData() {
    this.testDataList = [];
  }

  isValid() {
    if (this.testDataList.length === 0) {
      return false;
    }

    // Ensure all properties in each testData object are non-empty
    return this.testDataList.every((test) => {
      return Object.values(test).every((value) => value !== null && value !== "");
    });
  }


}

export default Patient;
