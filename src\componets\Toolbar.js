import React, { useState } from 'react';
import { Home, User, Settings, HelpCircle, Printer, Calculator, PackagePlus, ChevronDown,NotebookPen,FolderPlus,MonitorPlay,UploadCloudIcon,BookOpenCheck,Database,KeyRound } from 'lucide-react';

// 接收activeNav和setActiveNav作为props
const Toolbar = ({ onNavigate, activeNav, setActiveNav }) => {
  const [isCreateMenuOpen, setIsCreateMenuOpen] = useState(false); // State to toggle sub-menu visibility

  const toggleCreateMenu = () => {
    setIsCreateMenuOpen(!isCreateMenuOpen);
  };

  return (
    <nav style={{ backgroundColor: '#1a202c', padding: '1rem' }}>
      <ul style={{ display: 'flex', justifyContent: 'space-around', alignItems: 'center', listStyleType: 'none', margin: 0, padding: 0 }}>
        <li>
          <a href="#"  onClick={() => { setActiveNav('home'); onNavigate('home'); }} style={{ color: activeNav === 'home' ? 'red' : 'white', textDecoration: 'none', display: 'flex', alignItems: 'center' }}>
            <Home style={{ marginRight: '0.5rem' }} size={20} />
            主页
          </a>
        </li>
        <li>
          <a href="#" onClick={() => { setActiveNav('patient'); onNavigate('patient'); }} style={{ color: activeNav === 'patient' ? 'red' : 'white', textDecoration: 'none', display: 'flex', alignItems: 'center' }}>
               <User style={{ marginRight: '0.5rem' }} size={20} />
            标本信息
          </a>
        </li>
        {/* <li>
          <a href="#" onClick={() => onNavigate('resdata')} style={{ color: 'white', textDecoration: 'none', display: 'flex', alignItems: 'center' }}>
            <KeyRound style={{ marginRight: '0.5rem' }} size={20} />
            用药指导
          </a>
        </li> */}

         <li>
          <a href="#" onClick={() => { setActiveNav('mic'); onNavigate('mic'); }} style={{ color: activeNav === 'mic' ? 'red' : 'white', textDecoration: 'none', display: 'flex', alignItems: 'center' }}>
            <Calculator style={{ marginRight: '0.5rem' }} size={20} />
             MIC计算
          </a>
        </li>
       
        <li>
          <a href="#" onClick={() => { setActiveNav('report'); onNavigate('report'); }} style={{ color: activeNav === 'report' ? 'red' : 'white', textDecoration: 'none', display: 'flex', alignItems: 'center' }}>
            <Printer style={{ marginRight: '0.5rem' }} size={20} />
            试验报告
          </a>
        </li>

          <li>
          <a href="#" onClick={() => { setActiveNav('micInput'); onNavigate('micInput'); }} style={{ color: activeNav === 'micInput' ? 'red' : 'white', textDecoration: 'none', display: 'flex', alignItems: 'center' }}>
            <MonitorPlay style={{ marginRight: '0.5rem' }} size={20} />
            表型判定
          </a>
        </li>
       

        {/* <li>
          <a  href="#" onClick={() => { setActiveNav('aitech'); onNavigate('aitech'); }} style={{ color: activeNav === 'aitech' ? 'red' : 'white', textDecoration: 'none', display: 'flex', alignItems: 'center'}}>
                <MonitorPlay style={{ marginRight: '0.5rem' }} size={20} />
                  AI 预测
                </a>
        </li> */}

        {/* Create Menu with Sub-menu */}
        <li style={{ position: 'relative' }}>
          <a
            href="#"
            onClick={() => {
              setActiveNav('dict');
              toggleCreateMenu();
            }}
            style={{ color: activeNav === 'dict' ? 'red' : 'white', textDecoration: 'none', display: 'flex', alignItems: 'center' }}
          >
            <PackagePlus style={{ marginRight: '0.5rem' }} size={20} />
            医药词典
            <ChevronDown style={{ marginLeft: '0.5rem' }} size={16} />
          </a>
          {isCreateMenuOpen && (
            <ul style={{
              position: 'absolute',
              top: '100%',
              left: 0,
              backgroundColor: '#2d3748',
              listStyleType: 'none',
              padding: '0.5rem',
              margin: 0,
              borderRadius: '4px'
            }}>
              <li>
                <a
                  href="#"
                  onClick={() => {
                    onNavigate('bacteria');
                    setIsCreateMenuOpen(false); // Close the sub-menu on selection
                  }}
                  style={{ color: 'white', textDecoration: 'none', display: 'block', padding: '0.25rem 0', fontSize:'14px' }}
                >
                    <NotebookPen style={{ marginRight: '0.5rem' }} size={15} />
                  微生物种类
                </a>
              </li>
             
              <li>
                <a
                  href="#"
                  onClick={() => {
                    onNavigate('antibiotic');
                    setIsCreateMenuOpen(false); // Close the sub-menu on selection
                  }}
                  style={{ color: 'white', textDecoration: 'none', display: 'block', padding: '0.25rem 0',  fontSize:'14px' }}
                >
                   <HelpCircle style={{ marginRight: '0.5rem' }} size={15} />
                  抗菌药种类
                </a>
              </li>

              <li>
                <a
                  href="#"
                  onClick={() => {
                    onNavigate('specType');
                    setIsCreateMenuOpen(false); // Close the sub-menu on selection
                  }}
                  style={{ color: 'white', textDecoration: 'none', display: 'block', padding: '0.25rem 0',  fontSize:'14px' }}
                >
                   <MonitorPlay style={{ marginRight: '0.5rem' }} size={15} />
                  标本种类
                </a>
              </li>

              <li>
                <a
                  href="#"
                  onClick={() => {
                    onNavigate('create');
                    setIsCreateMenuOpen(false); // Close the sub-menu on selection
                  }}
                  style={{ color: 'white', textDecoration: 'none', display: 'block', padding: '0.25rem 0',  fontSize:'14px' }}
                >
                  <FolderPlus style={{ marginRight: '0.5rem' }} size={15} />
                  MIC编辑
                </a>
              </li>
              <li>
                <a
                  href="#"
                  onClick={() => {
                    onNavigate('sample');
                    setIsCreateMenuOpen(false); // Close the sub-menu on selection
                  }}
                  style={{ color: 'white', textDecoration: 'none', display: 'block', padding: '0.25rem 0',  fontSize:'14px' }}
                >
                  <BookOpenCheck style={{ marginRight: '0.5rem' }} size={15} />
                  药物推导
                </a>
              </li>

              <li>
                <a
                  href="#"
                  onClick={() => {
                    onNavigate('whonet');
                    setIsCreateMenuOpen(false); // Close the sub-menu on selection
                  }}
                  style={{ color: 'white', textDecoration: 'none', display: 'block', padding: '0.25rem 0',  fontSize:'14px' }}
                >
                  <KeyRound style={{ marginRight: '0.5rem' }} size={15} />
                  专家规则
                </a>
              </li>
              <li>
                <a
                  href="#"
                  onClick={() => {
                    onNavigate('upload');
                    setIsCreateMenuOpen(false); // Close the sub-menu on selection
                  }}
                  style={{ color: 'white', textDecoration: 'none', display: 'block', padding: '0.25rem 0',  fontSize:'14px' }}
                >
                  <UploadCloudIcon style={{ marginRight: '0.5rem' }} size={15} />
                  专家接口
                </a>
              </li>

            </ul>
          )}
        </li>

        <li>
          <a href="#" onClick={() => { setActiveNav('help'); onNavigate('help'); }} style={{ color: activeNav === 'help' ? 'red' : 'white', textDecoration: 'none', display: 'flex', alignItems: 'center' }}>
            <HelpCircle style={{ marginRight: '0.5rem' }} size={20} />
            帮助
          </a>
        </li>
      </ul>
    </nav>
  );
};

export default Toolbar;
