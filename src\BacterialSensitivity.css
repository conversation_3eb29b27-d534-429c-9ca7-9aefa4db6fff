.bacter_container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 24px;
  background-color: #282c34;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  color: white;
  font-size: 14px;
}

.bacter_header {
  margin-bottom: 24px;
}

.bacter_title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 8px;
  color: white;
}

.bacter_subtitle {
  color: #ffffff99;
  margin-bottom: 20px;
  font-size: 14px;
}

.bacter_table_container {
  overflow-x: auto;
}

.bacter_table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 24px;
  font-size: 14px;
}

.bacter_table td {
  border: 1px solid #404756;
  padding: 12px;
  color: white;
}

.bacter_table tr:hover {
  background-color: #2c313a;
  transition: background-color 0.2s ease;
}

.bacter_sensitive {
  color: #6ee7b7;
  font-weight: 500;
}

.bacter_resistant {
  color: #fca5a5;
  font-weight: 500;
}

.bacter_interpretation_title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 16px;
  color: white;
}

.bacter_interpretation {
  color: white;
  line-height: 1.6;
  font-size: 14px;
}

.bacter_interpretation_list {
  padding-left: 16px;
}

.bacter_interpretation_item {
  margin-bottom: 8px;
  color: white;
  font-size: 14px;
}

.bacter_interpretation_item:last-child {
  margin-bottom: 0;
}

/* Previous styles remain the same */

.bacter_table th {
  background-color: #7fa6f5;  /* Light gray background like the document */
  border: 1px solid #404756;
  padding: 12px;
  text-align: left;
  width: 16.5%;
  height: 50%;
  color: #333;    /* Dark text color for better contrast with light background */
  font-weight: normal;  /* Normal font weight as in document */
}

/* Add a specific class for the header cells that need bold text */
.bacter_table th.bacter_header_main {
  font-weight: bold;
}

.bacteria_mic-input {
 
  width: 100%; /* Makes the select box as wide as its container */
  padding:8px; /* Adds padding inside the select box */
  font-size: 14px; /* Sets a readable font size */
  border: 1px solid #ccc; /* Adds a light gray border */
  border-radius: 4px; /* Rounds the corners of the select box */
  background-color: #f9f9f9; /* Light gray background */
  color: #333; /* Darker text color */
  box-sizing: border-box;
}
.bacteria_submit-button-container {
  display: flex;
  justify-content: center; /* 使按钮居中 */
  margin-top: 10px; /* 为按钮添加顶部间距 */
}
.bacteria_submit-button {
  background-color: #4CAF50; /* 绿色背景 */
  color: white; /* 白色文字 */
  padding: 10px 20px; /* 内边距 */
  border: none; /* 无边框 */
  border-radius: 5px; /* 圆角 */
  cursor: pointer; /* 鼠标悬停效果 */
  font-size: 16px; /* 字体大小 */
  margin-top: 10px; /* 顶部边距 */
  transition: background-color 0.3s; /* 背景色过渡效果 */
 
}


.bacteria_reset-button {
  background-color: #3b82f6;;
  color: white; /* 白色文字 */
  padding: 10px 20px; /* 内边距 */
  border: none; /* 无边框 */
  border-radius: 5px; /* 圆角 */
  cursor: pointer; /* 鼠标悬停效果 */
  font-size: 16px; /* 字体大小 */
  margin-top: 10px; /* 顶部边距 */
  transition: background-color 0.3s; /* 背景色过渡效果 */
  margin-left: 10px; 
}
/* Rest of the styles remain the same */