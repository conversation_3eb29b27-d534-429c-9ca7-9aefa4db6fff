import React, { useState } from 'react';
import { Upload, Download,Printer } from "lucide-react";
import './DrugResistantanceTable.css';

const DrugResistanceTable = () => {
  const [fileName, setFileName] = useState('');
  const [drugData, setDrugData] = useState([
    { id: 1, antibiotic: '厄他培南', breakpoint: '<=18; >=22', result: '10mm', resistance: '耐药' },
    { id: 2, antibiotic: '亚胺培南', breakpoint: '<=1; >=4', result: '16 μg/mL', resistance: '耐药' },
    { id: 3, antibiotic: '美罗培南', breakpoint: '<=1; >=4', result: '32 μg/mL', resistance: '耐药' },
    { id: 4, antibiotic: '头孢叱肟', breakpoint: '<=2; >=16', result: '>32 μg/mL', resistance: '耐药' },
    { id: 5, antibiotic: '头孢他啶', breakpoint: '<=4; >=16', result: '>32 μg/mL', resistance: '耐药' },
    { id: 6, antibiotic: '头孢曲松', breakpoint: '<=1; >=4', result: '>32 μg/mL', resistance: '耐药' },
    { id: 7, antibiotic: '头孢哌酮/舒巴坦', breakpoint: '<=16; >=64', result: '>128 μg/mL', resistance: '耐药' },
    { id: 8, antibiotic: '哌拉西林/他唑巴坦', breakpoint: '<=8; >=32', result: '>256 μg/mL', resistance: '耐药' },
    { id: 9, antibiotic: '阿米卡星', breakpoint: '<=4; >=16', result: '2 μg/mL', resistance: '敏感' },
    { id: 10, antibiotic: '庆大霉素', breakpoint: '<=14; >=18', result: '6mm', resistance: '耐药' },
    { id: 11, antibiotic: '复方新诺明', breakpoint: '<=2; >=4', result: '>16 μg/mL', resistance: '耐药' },
    { id: 12, antibiotic: '氨曲南', breakpoint: '<=4; >=16', result: '<=1 μg/mL', resistance: '敏感' },
    { id: 13, antibiotic: '环丙沙星', breakpoint: '<=0.25; >=1', result: '0.5 μg/mL', resistance: '中介' },
    { id: 14, antibiotic: '替加环素', breakpoint: '<=2; >=8', result: '0.25 μg/mL', resistance: '敏感' },
    { id: 15, antibiotic: '多粘菌素', breakpoint: '<=2; >=4', result: '0.125 μg/mL', resistance: '敏感' },
    { id: 16, antibiotic: '头孢他啶/阿维巴坦', breakpoint: '<=8; >=16', result: '>64 μg/mL', resistance: '耐药' },
    { id: 17, antibiotic: '磺霉素', breakpoint: '<=12; >=16', result: '6mm', resistance: '耐药' },
    { id: 18, antibiotic: '左氧氟沙星', breakpoint: '<=16; >=21', result: '24mm', resistance: '敏感' },
    { id: 19, antibiotic: '呋喃妥因', breakpoint: '<=14; >=17', result: '22mm', resistance: '敏感' },
    { id: 20, antibiotic: '依拉环素', breakpoint: '<=1', result: '0.125 μg/mL', resistance: '敏感' }
  ]);

  const handleFileImport = (event) => {
    const file = event.target.files[0];
    if (file) {
      setFileName(file.name);
    }
  };

  return (
    <div className="drug-resistance-container">
      <div className="header">
        <div className="file-import-section">
        
       
     <input
          type="file"
          accept=".xlsx, .xls"
         
          style={{ display: "none" }}  // 隐藏原生的上传按钮
          id="file-input"
        />
    <button
          type="button"
          onClick={() => document.getElementById("file-input").click()}
          title="导入"
          style={{ background: "none", border: "none", cursor: "pointer" }}
        >
          <Download size={20} color='black'/>
        </button>

    {/* 下载按钮 */}
    <button  type="button"  title="导出" 
      style={{ background: "none", border: "none", cursor: "pointer" }}
    >
      <Upload size={20} color='black' />
    </button>
  
    <button  type="button"  title="打印" 
      style={{ background: "none", border: "none", cursor: "pointer" }}
    >
      <Printer size={20} color='black' />
    </button>
  

        </div>
        <h2 className="table-title">大肠埃希菌 - 抗菌药物敏感性结果</h2>
      </div>
      <div className="table-container">
        <table className="drug-resistance-table">
          <thead>
            <tr>
              <th>序号</th>
              <th>抗菌药物</th>
              <th>折点</th>
              <th>结果</th>
              <th>解释</th>
            </tr>
          </thead>
          <tbody>
            {drugData.map((drug) => (
              <tr key={drug.id}>
                <td>{drug.id}</td>
                <td>{drug.antibiotic}</td>
                <td>{drug.breakpoint}</td>
                <td>{drug.result}</td>
                <td className={`resistance-${drug.resistance.toLowerCase()}`}>
                  {drug.resistance}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default DrugResistanceTable;
