const mongoose = require('mongoose');

const PatientSchema = new mongoose.Schema({
  patient_id: { type: String, required: true, unique: true }, // 患者ID，唯一
  hospital_code: { type: String },
  name: { type: String },
  gender: { type: String, default: '男' },
  age: { type: String },
  department: { type: String },
  ward: { type: String },
  bed: { type: String },
  medicalRecordNumber: { type: String },
  specimenNumber: { type: String },
  consumableboardNumber: { type: String },
  sampleType: { type: String },
  sampleCode: { type: String },
  bacteriaCount: { type: String },
  cultureResult: { type: String },
  bacteriaCode: { type: String },
  smearResult: { type: String },
  remark: { type: String },
  testDataList: [
    {
      
      testMethod: { type: String },
      testResult: { type: String },
      testSupplement: { type: String },
      testSupplementResult: { type: String },
    
    },
  ],
  createdAt: { type: Date, default: Date.now }, // 创建时间
  updatedAt: { type: Date, default: Date.now }, // 更新时间
});

// 在保存前更新 updatedAt 字段
PatientSchema.pre('save', function (next) {
  this.updatedAt = Date.now();
  next();
});

module.exports = mongoose.model('Patient', PatientSchema);
