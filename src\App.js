import React, { useState } from 'react';
import Toolbar from './componets/Toolbar';
import HomePage from './HomePage';
import MICForm from './MICForm';
import EditableTable from './EditableTable';
import PatientForm from './PatientForm';
import PatientReport from './PatientReport'
import HelpPage from './HelpPage';
import TaxonomyTree from './componets/TaxonomyTree';
import AntibioticsManager from './componets/AntibioticsManager';
import StrainPredictor from './StrainPredictor';
import SpecManager from './componets/SpecManager';
import ILabLinkConverter from './ILabLinkConverter ';
import BacteriaForm from './BacteriaForm';
import BacterialSensitivityResults from './BacterialSensitivityResults';
import DrugResistanceTable from './DrugResistanceTable';
import AntibioticResistanceAlerts from './componets/AntibioticResistanceAlerts';
import { ToastProvider } from './componets/ToastProvider';
import './App.css'
import LabReportInterface from './LabReportInterface';

const App = () => {
  const [currentPage, setCurrentPage] = useState('home');
  const [activeNav, setActiveNav] = useState('home');

  const handleNavigation = (page) => {
    setCurrentPage(page);
    setActiveNav(page); // 同步高亮导航
  };

  const renderPage = () => {
    switch(currentPage) {
      case 'home': return <div className="App-header"><HomePage/></div>;
      case 'patient': return <div className="App-header"><PatientForm handleNavigation={handleNavigation}/></div>;
      case 'create':  return <div className="App-header"><EditableTable/></div>;
      case 'mic':  return <div className="App-header"><ToastProvider><MICForm handleNavigation={handleNavigation}/></ToastProvider></div>;
      case 'micInput':  return <div className="App-header"><ToastProvider><LabReportInterface handleNavigation={handleNavigation}/></ToastProvider></div>;
      case 'report':  return <div className="App-header"><PatientReport/></div>;
      case 'resdata':  return <div className="App-header"><BacterialSensitivityResults/></div>;
      case 'sample':  return <div className="App-header"><BacteriaForm/></div>;
      case 'bacteria': return <div className="App-header"><TaxonomyTree/></div>;
      case 'batch': return <div className="App-header"><DrugResistanceTable/></div>;
      case 'aitech': return <div className="App-header"><StrainPredictor/></div>;
      case 'antibiotic': return <div className="App-header"><AntibioticsManager/></div>;
      case 'specType': return <div className="App-header"><SpecManager/></div>;
      case 'upload': return <div className="App-header"><ILabLinkConverter/></div>;
      case 'whonet': return <div className="App-header"><AntibioticResistanceAlerts/></div>;
      case 'help':  return <div className="App-header"><HelpPage/></div>;
      default: return <div className="App-header"><HomePage /></div>;
    }
  };

  return (
    <div>
      <Toolbar onNavigate={handleNavigation} activeNav={activeNav} setActiveNav={setActiveNav} />
      {renderPage()}
    </div>
  );
};

export default App;