const mongoose = require('mongoose');

// 定义 Bacteria Schema
const BacteriaSchema = new mongoose.Schema({
  bacteria_name: { type: String, required: true, unique: true },  // 细菌名
  specType: { type: String },  // 细菌名
  
  antibiotics: [
    {
      name: { type: String, required: true },  // 抗菌药物名
      result: { type: String, required: true }  // 药物结果（敏感/耐药）
    }
  ],
  interpretation: { type: String, required: true },  // 解释
  interpretation_name: { type: String, required: true },  // 解释名称
  create_date: { type: Date, default: Date.now },  // 创建日期
  update_date: { type: Date, default: Date.now }   // 更新日期
}, { collection: 'Interpretation' });

// 在保存之前更新 update_date 字段
BacteriaSchema.pre('save', function (next) {
  this.update_date = Date.now();
  next();
});

// 导出模型，使用 'Bacteria' 作为模型名称
module.exports = mongoose.model('Bacteria', BacteriaSchema);
