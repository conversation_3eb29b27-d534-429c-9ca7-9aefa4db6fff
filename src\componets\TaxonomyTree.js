// components/TaxonomyTree.js
import React, { useEffect, useState } from 'react';
import { Tree, Button, Modal, message } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import TaxonomyForm from './TaxonomyForm';
import { fetchTaxonomyTree, updateTaxonomyNode,deleteTaxonomyNode,addOrEditNode } from '../services/taxonomyService';

const TaxonomyTree = () => {
  const [treeData, setTreeData] = useState([]);
  const [selectedNode, setSelectedNode] = useState(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [editMode, setEditMode] = useState(false);
  
  useEffect(() => {
    loadTreeData();
    }, []);

  // 加载树形数据
  const loadTreeData = async () => {
    try {
      const data = await fetchTaxonomyTree();
      setTreeData(formatTreeData(data));
    } catch (error) {
      message.error('加载分类数据失败');
    }
  };

  // 格式化树形数据
  const formatTreeData = (data) => {
    return data.map((node) => ({
      title: node.name,
      key: node._id,
      level:node.level,
      children: formatTreeData(node.children || [])
    }));
  };



  // 添加或编辑节点
  const handleNodeAddOrEdit = async (values) => {
    setModalVisible(false);
    if(editMode){
        await updateTaxonomyNode(values.key,values.name);
    }
    else
    {
     const data ={name:values.name,level:selectedNode==null?1:selectedNode.level+1,parent_id:selectedNode==null?null:selectedNode.key }
     await addOrEditNode(data);
    }
    loadTreeData(); // 重新加载树数据
    setSelectedNode(null);
    message.success(editMode ? '节点已更新' : '节点已添加');
  };

  // 删除节点
  const handleDeleteNode = async (key) => {
    try {
      await deleteTaxonomyNode(key);
      message.success('节点已删除');
      loadTreeData(); // 重新加载树数据
      setSelectedNode(null);
    } catch (error) {
      message.error('删除失败');
    }
  };

  return (
    <div>
      <Button
        type="primary"
        icon={<PlusOutlined />}
        onClick={() => {
          setEditMode(false);
          // setSelectedNode(null);
          setModalVisible(true);
        }}
        style={{ marginBottom: 16 }}
      >
        添加新名称
      </Button>
      <Tree
        treeData={treeData}
        onSelect={
          (keys, event) => {
              //  console.log("Event Node:", event.node);
              setSelectedNode(event.node)
          }
        }
      />

      {selectedNode && (
        <div style={{ marginTop: 16 }}>
          <Button
            icon={<EditOutlined />}
            onClick={() => {
                         
              setEditMode(true);
              setModalVisible(true);
            }}
          >
            编辑
          </Button>
          <Button
            icon={<DeleteOutlined />}
            danger
            onClick={() => handleDeleteNode(selectedNode.key)}
            style={{ marginLeft: 8 }}
          >
            删除
          </Button>
        </div>
      )}

      {/* 添加或编辑模态框 */}
      <Modal
        title={editMode ? '编辑节点' : '添加节点'}
        visible={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
      >
        <TaxonomyForm
          onSubmit={handleNodeAddOrEdit}
          initialValues={editMode ? selectedNode : null}
        />
      </Modal>
    </div>
  );
};

export default TaxonomyTree;
