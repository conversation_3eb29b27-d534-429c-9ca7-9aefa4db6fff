import React, { useState,useRef,useEffect } from "react";
import { Tabs } from "antd";
import MICForm from "./MICForm"; 
import { ShieldQuestion, Search} from 'lucide-react';
import { useSharedState } from './SharedStateContext';
import './QueryForm.css';

const { TabPane } = Tabs;

const MICWithTabs = () => {
  const [recentQueries, setRecentQueries] = useState([]);
  const [activeTab, setActiveTab] = useState("0"); 
  const micFormRef = useRef(null);
  const { state, dispatch } = useSharedState();
  
useEffect(() => {
  //  setRecentQueries([]);
  const savedQueries = localStorage.getItem("recentQueries");
  setRecentQueries(savedQueries ? JSON.parse(savedQueries) : []);
 
  // console.log("init",recentQueries);
}, []);


 
const handleSaveQuery = (query) => {
  const queries = recentQueries || []; // 确保 queries 是数组
  const queryExists = queries.some(item => 
    item.queryString.bacteriaName === query.bacteriaName && 
    item.queryString.selectedAgent === query.selectedAgent
  );
  
  if (!queryExists) {
    const updatedQueries = [{ queryString: query }, ...queries.slice(0, 9)]; // 添加新查询
    setRecentQueries(updatedQueries);
    localStorage.setItem("recentQueries", JSON.stringify(updatedQueries));
  }
};


  const handleRecentQueryClick = (query) => {
  
    if (micFormRef.current) {
      
      micFormRef.current.doSearch(query); 
      setActiveTab("0");
    }
  };

  const handleTabChange = (key) => {
    if (key === "1" && micFormRef.current) {
      const updatedQueries = localStorage.getItem("recentQueries");
  
      if (updatedQueries) {
        try {
          setRecentQueries(JSON.parse(updatedQueries));
        } catch (e) {
          console.error("Error parsing recentQueries:", e);
          setRecentQueries([]);
        }
      } else {
        setRecentQueries([]); // Fallback to an empty array if no data exists
      }
    }
    setActiveTab(key);
  };
  
  const clearRecentQueries = () => {
    localStorage.removeItem("recentQueries");
    setRecentQueries([]);
  };
  
  if(!state.patient)
  return (
    <div className="report-container">
      <div className="patient-selection-content">
        <ShieldQuestion size={35} />
        <span className="patient-selection-text">还没有选择标本信息!!</span>
      </div>
    </div>
  );

  return (
    <div className="container">
      <Tabs activeKey={activeTab} onChange={handleTabChange}>
        {/* Tab 0: MIC Form */}
        <TabPane tab="MIC 折点计算" key="0" >
        <MICForm ref={micFormRef} onSaveQuery={handleSaveQuery} />
        </TabPane>

        {/* Tab 1: Recent Query Conditions */}
        <TabPane tab="快捷查询" key="1">
          <div style={{ padding: "20px" }}>
            <h6 style={{ color: "white", fontSize: "16px" }}>最近的查询</h6>
            <button
              onClick={clearRecentQueries}
              style={{
                backgroundColor: "#e74c3c",
                color: "white",
                border: "none",
                padding: "10px",
                borderRadius: "5px",
                cursor: "pointer",
                marginBottom: "10px",
              }}
            >
              清除历史记录
            </button>
            {!recentQueries ||recentQueries.length === 0 ? (
              <p style={{ color: "gray" }}>暂无最近查询记录</p>
            ) : (
             
              <table
              style={{
                width: "100%",
                borderCollapse: "collapse",
                color: "white",
                marginTop: "10px",
              }}
            >
              <thead>
                <tr>
                  <th style={{ border: "1px solid #444", padding: "8px" }}>细菌</th>
                  <th style={{ border: "1px solid #444", padding: "8px" }}>抗菌药物</th>
                  <th style={{ border: "1px solid #444", padding: "8px" }}>MIC</th>
                  <th style={{ border: "1px solid #444", padding: "8px", textAlign: "center" }}>操作</th>
                </tr>
              </thead>
              <tbody>
                {recentQueries && recentQueries.length > 0 ? (
                  recentQueries.map((query, index) => (
                    <tr key={index}>
                      <td style={{ border: "1px solid #444", padding: "8px" }}>
                        {query.queryString.bacteriaName}
                      </td>
                      <td style={{ border: "1px solid #444", padding: "8px" }}>
                        {query.queryString.selectedAgent}
                      </td>
                      <td style={{ border: "1px solid #444", padding: "8px" }}>
                        {query.queryString.micValue}
                      </td>
                      <td
                        style={{
                          border: "1px solid #444",
                          padding: "8px",
                          textAlign: "center",
                        }}
                      >
                        <button
                          style={{
                            background: "none",
                            border: "none",
                            cursor: "pointer",
                            color: "white",
                          }}
                          title="查询"
                          onClick={() => handleRecentQueryClick(query.queryString)}
                        >
                          <Search size={20} />
                        </button>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td
                      colSpan="4"
                      style={{
                        border: "1px solid #444",
                        padding: "8px",
                        textAlign: "center",
                      }}
                    >
                      暂无查询记录
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
            
              )}
          </div>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default MICWithTabs;
