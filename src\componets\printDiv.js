const printDiv = (className) => {
  const reportContent = document.querySelector(`.${className}`);
  
  if (!reportContent) {
    console.error(`No element found with class: ${className}`);
    return;
  }

  const computedStyles = window.getComputedStyle(reportContent);
  
  const printWindow = window.open('', '_blank');
  printWindow.document.open();
  
  const styleSheets = Array.from(document.styleSheets);
  const styleLinks = styleSheets
    .map(styleSheet => {
      if (styleSheet.href) {
        return `<link rel="stylesheet" href="${styleSheet.href}" />`;
      }
      return '';
    })
    .join('');

  const styleTags = Array.from(document.getElementsByTagName('style'))
    .map(style => style.outerHTML)
    .join('');

  printWindow.document.write(`
    <!DOCTYPE html>
    <html>
      <head>
        <title>Print Report</title>
        ${styleLinks}
        ${styleTags}
        <style>
          /* Reset and full-height setup */
          html, body {
            margin: 0;
            padding: 0;
            min-height: 100vh;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: auto;
            background-color: #f5f5f5; /* Light grey background for better preview */
          }

          /* Main wrapper for perfect centering */
          .print-wrapper {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            min-height: 100vh;
            padding: 2rem;
            box-sizing: border-box;
          }

          /* Inner container for content centering */
          .content-container {
            display: flex;
            justify-content: center;
            align-items: center;
            background: white; /* White background for content */
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* Subtle shadow for preview */
          }

          /* Report container styling */
          .${className} {
            ${Array.from(computedStyles).map(key => 
              `${key}: ${computedStyles.getPropertyValue(key)};`
            ).join('\n')}
            margin: 0 !important;
            float: none !important;
            position: relative !important;
            left: auto !important;
            right: auto !important;
            transform: none !important;
            min-width: max-content !important;
          }

          /* Table preservation */
          .${className} table {
            width: 100% !important;
            max-width: 100% !important;
            table-layout: auto !important;
            border-collapse: collapse !important;
          }

          .${className} td,
          .${className} th {
            word-wrap: break-word !important;
            overflow: hidden !important;
          }

          /* Print media styles */
          @media print {
            html, body {
              background: none !important;
              height: auto !important;
              min-height: auto !important;
              overflow: visible !important;
              display: block !important;
            }
            
            .print-wrapper {
              padding: 0 !important;
              min-height: auto !important;
              display: block !important;
              box-shadow: none !important;
            }

            .content-container {
              padding: 0 !important;
              box-shadow: none !important;
              background: none !important;
              border-radius: 0 !important;
            }

            .no-print {
              display: none !important;
            }
          }
        </style>
      </head>
      <body>
        <div class="print-wrapper">
          <div class="content-container">
            ${reportContent.outerHTML}
          </div>
        </div>
      </body>
    </html>
  `);
  
  printWindow.document.close();

  printWindow.onload = function() {
    setTimeout(() => {
      printWindow.focus();
      printWindow.print();
      printWindow.onafterprint = function() {
        printWindow.close();
      };
    }, 250);
  };
};

export default printDiv;
