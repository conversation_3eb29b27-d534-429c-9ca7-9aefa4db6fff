.compoound-container {
    width: 100%;
    max-width: 1580px; /* 设置最大宽度 */
    margin: 0 auto;
    padding: 20px;
    box-sizing: border-box; /* 确保内边距不影响整体宽度 */
   
  }

.compound-table-container {
    width: 100%;
     overflow-x: auto;
     justify-content: center;
  }
  
  .compound-table {
    width: 100%;
    min-width: 675px;
    border-collapse: separate;
    border-spacing: 0;
    text-align: center;
   

  }
  
  .compound-table th,
  .compound-table td {
    border: 1px solid #2b7c7c;
    padding: 8px;
    border-left: none;
    color: white;
  }
  
  .compound-table th:first-child,
  .compound-table td:first-child {
    border-left: 1px solid #2b7c7c;
  }
  
  .compound-table thead th {
    background-color: #2b7c7c;
    color: white;
    font-weight: normal;
    vertical-align: middle;
    font-size: 14px;
  }

  .compound-table tbody td {
    background-color: #282c34; /* 更新为指定的背景色 */
    min-width: 90px;
    font-size: 14px;
  }

  .text-left {
    text-align: left;
    padding-left: 20px !important; /* 给左对齐的文本添加一些缩进 */
  }
  
  /* 组标题之间的实线分隔 */
  .group-header {
    border-right: 2px solid #fff !important;
  }
  
  .group-header:last-of-type {
    border-right: 1px solid #2b7c7c !important;
  }
  
  /* 子标题单元格的右边框 */
  .sub-header th:nth-child(4) {
    border-right: 2px solid #2b7c7c;
  }
  
  /* 数据行的分组分隔线 */
  .compound-table tbody td:nth-child(6) {
    border-left: 2px solid #2b7c7c;
  }
  
  .compound-table tbody td {
    min-width: 60px;
  }
  
  .first-col {
    min-width: 120px;
  }
  
  /* 响应式样式 */
  @media screen and (max-width: 768px) {
    .compound-table {
      font-size: 14px;
    }
    
    .compound-table th,
    .compound-table td {
      padding: 4px;
    }
  }