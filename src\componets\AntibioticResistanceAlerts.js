import React, { useState, useEffect } from 'react';
import { Table, Input, Button, Form, message,Select,Modal  } from 'antd';
import axios from 'axios';
import '../QueryForm.css'; 
const AntibioticResistanceAlerts = () => {
  const [form] = Form.useForm();  // 表单实例
  const [data, setData] = useState([]);  // 存储从API获取的数据
  const [loading, setLoading] = useState(false);  // 加载状态
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [selectedRow, setSelectedRow] = useState(null);
  const BASE_URL = process.env.REACT_APP_BASE_URL;
  // 获取抗生素数据
  const fetchData = async () => {
    try {
      setLoading(true);
      const response = await axios.get(`${BASE_URL}whonetRules`);
      setData(response.data);
    } catch (error) {
      message.error('加载whonet数据失败');
    } finally {
      setLoading(false);
    }
  };

  const handleRowClick = (record) => {
    setSelectedRow(record);
    setIsModalVisible(true);
  };

  // 关闭 Modal
  const handleCloseModal = () => {
    setIsModalVisible(false);
    setSelectedRow(null);
  };

  useEffect(() => {
    fetchData();  // 页面加载时获取数据
  }, []);

  const handleReset = () => {
    form.resetFields();  // 清空所有输入框
    fetchData();
  };


  // 查询抗生素数据
  const handleQuery = async () => {
    try {
      const values = form.getFieldsValue();
      if (!values.ORG_CODES) {
        message.warning('请输入菌株代码');
        return;
      }

      setLoading(true);

      const response = await axios.get(`${BASE_URL}whonetRules/${values.ORG_CODES}`);
      
      if (response.data) {
       
        setData(response.data);
        message.success('查询成功');
      } else {
        message.warning('未找到相关数据');
      }

    } catch (error) {
      message.error('查询失败：' + (error.response?.data?.message || '未找到该菌种'));
    } finally {
      setLoading(false);
    }
  };




  // 表格列定义
  const columns = [
    // {
    //   title: '规则集',
    //   dataIndex: 'RuleSet',
    //   key: 'RuleSet',
    // },
    // {
    //   title: '专家规则类型',
    //   dataIndex: 'EXPERTTYPE',
    //   key: 'EXPERTTYPE',
    // },
    // {
    //   title: '分类',
    //   dataIndex: 'CATEGORY',
    //   key: 'CATEGORY',
    // },
    // {
    //   title: '是否启用',
    //   dataIndex: 'ACTIVE',
    //   key: 'ACTIVE',
    // },
    // {
    //   title: '优先级',
    //   dataIndex: 'PRIORITY',
    //   key: 'PRIORITY',
    // },
    {
      title: '菌种缩写编码',
      dataIndex: 'ORG_CODES',
      key: 'ORG_CODES',
    },
    {
      title: '菌名（英文)',
      dataIndex: 'ORGANISMS',
      key: 'ORGANISMS',
    },
    
    {
      title: '菌群类别',
      dataIndex: 'ORG_GROUP',
      key: 'ORG_GROUP',
    },
    {
      title: '规则描述',
      dataIndex: 'DESCRIPTION',
      key: 'DESCRIPTION',
    }
    ,
    // {
    //   title: '抗生素及结果条件',
    //   dataIndex: 'ABX_RESULT',
    //   key: 'ABX_RESULT',
    // },
    // {
    //   title: '其他检测结果条件',
    //   dataIndex: 'OTH_RESULT',
    //   key: 'OTH_RESULT',
    // },
    // {
    //   title: '解释说明',
    //   dataIndex: 'INTERP',
    //   key: 'INTERP',
    // },
    // {
    //   title: '微生物学注释',
    //   dataIndex: 'MICROBIOL',
    //   key: 'MICROBIOL',
    
    // },
    // {
    //   title: '临床注释',
    //   dataIndex: 'CLINICAL',
    //   key: 'CLINICAL',
    // },
    // {
    //   title: '质量控制建议',
    //   dataIndex: 'QUALITY',
    //   key: 'QUALITY',
    // },
    // {
    //   title: '质量控制类型',
    //   dataIndex: 'QUAL_TYPE',
    //   key: 'QUAL_TYPE',
    // },
    // {
    //   title: '是否重要菌种(X 表示是)',
    //   dataIndex: 'IMP_SPECIE',
    //   key: 'IMP_SPECIE',
    // },
    // {
    //   title: '是否重要耐药(X 表示是)',
    //   dataIndex: 'IMP_RESIST',
    //   key: 'IMP_RESIST',
    // },
    // {
    //   title: '是否保存菌株(X 表示是)',
    //   dataIndex: 'SAVE_ISOL',
    //   key: 'SAVE_ISOL',
    // },
    // {
    //   title: '是否送参考实验室(X 表示是)',
    //   dataIndex: 'SEND_REF',
    //   key: 'SEND_REF',
    // },
    // {
    //   title: '感染控制建议',
    //   dataIndex: 'INF_CONT',
    //   key: 'INF_CONT',
    // },
    // {
    //   title: '用药建议备注',
    //   dataIndex: 'RX_COMMENT',
    //   key: 'IRX_COMMENT',
    // }
   
  ];

  return (
    <div class="container">
      <Form form={form} layout="vertical">
        <Form.Item label={<span style={{ color: 'white' }}>菌种缩写编码</span>} name="ORG_CODES" rules={[{ required: true, message: '请输入菌种缩写编码' }]}>
          <Input placeholder="输入菌种缩写编码" className='mic-input' />
        </Form.Item>
      
       
        <div style={{ display: 'flex', justifyContent: 'space-between', width:'80%' }}>
          <Button type="primary" onClick={handleQuery}>
            查询
          </Button>
           <Button onClick={handleReset} style={{ marginLeft: 8 }}>
            重置
          </Button>
        </div>
      </Form>
      <Table
        columns={columns}
        dataSource={data}
        rowKey="_id"
        loading={loading}
        scroll={{ x: 1000 }}  // 允许横向滚动，限制宽度  
        style={{ marginTop: '20px',width:'80%' }}
        onRow={(record) => ({
          onClick: () => handleRowClick(record)
        })}
      /> 
       <Modal
        title="详细信息"
        visible={isModalVisible}
        onCancel={handleCloseModal}
        footer={null}
        width={700}
      >
        {selectedRow &&
          Object.entries(selectedRow).map(([key, value]) => (
            <div key={key} style={{ marginBottom: '8px' }}>
              <strong>{key}:</strong>
              <div style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-word', marginLeft: '10px' }}>
                {value || '（空）'}
              </div>
            </div>
          ))}
      </Modal>
    </div>
  );
};

export default AntibioticResistanceAlerts;
