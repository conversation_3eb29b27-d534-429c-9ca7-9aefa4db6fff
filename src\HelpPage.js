import React, { useState } from 'react';
import './HelpPage.css';
// HelpSection Component
const HelpSection = ({ section, isExpanded, onToggle }) => {
  return (
    <div className={`help-section ${isExpanded ? 'expanded' : ''}`}>
      <button className="help-section-header" onClick={onToggle}>
        <div className="help-section-title">
          <span className="icon">{section.icon}</span>
          <span>{section.title}</span>
        </div>
        <span className="arrow">▶</span>
      </button>
      <div className="help-section-content">
        <div className="help-section-text">
          {section.content.split('\n').map((line, index) => (
            <React.Fragment key={index}>
              {line}
              <br />
            </React.Fragment>
          ))}
        </div>
      </div>
    </div>
  );
};

// Footer Component
const HelpFooter = () => {
  return (
    <div className="help-footer">
      <div className="help-footer-content">
        <span className="icon">❓</span>
        <div className="help-footer-text">
          <h3>需要更多帮助？</h3>
          <p>如果您在使用过程中遇到任何问题，请联系技术支持团队。我们的支持人员将很乐意为您提供帮助。</p>
          <div className="help-footer-buttons">
            <button className="btn primary" onClick={() => window.alert('联系支持')}>联系支持</button>
            <button className="btn secondary" onClick={() => window.alert('查看文档')}>查看文档</button>
          </div>
        </div>
      </div>
    </div>
  );
};

// Main HelpPage Component
const HelpPage = () => {
  const [expandedSectionId, setExpandedSectionId] = useState(null);

  const sections = [
    {
      id: 'patient-management',
      title: '标本信息',
      icon: '👥',
      content: `该模块支持患者基本信息和检测信息的全面管理：
• 添加和删除患者信息,对于已有患者可以通过患者编号导入后编辑
• 标本信息中包括检测数据，从选择"标本编号"开始到"解析分类"结束，点击生成检测数据按钮，每次完成一次测试数据登记，就出现一条新的测试记录，用户可以多次循环完成检测记录
• 标本信息中标本编号可以点击右边的按钮自动生成
• 标本信息中标本类型可以输入标本代码自动生成，标本代码见医药词典目录的标本种类
• 检测数据中相同的标本类型,可以重复标本编号，比如验血， 可以有相同的编号多个检查方法，但不同的标本类型，必须产生一个新的标本编号
• 点击"提交" 完成文档保存或修改，点击"重置" 重新选择患者， 点击"删除" 删除当前患者, 点击"查看检测数据" 系统弹出测试数据列表，用户可以删除单条记录。
• 无论是新建患者还是导入的患者，必须点击提交按钮以便激活
• 记录患者姓名、性别、年龄和科室信息
• 快速搜索和筛选患者记录
• 患者历史记录查询
• 记录涂片结果
• 培养方法记录
• 实验结论录入
• 样本追踪和查询`
    },
//     {
//       id: 'mic-test',
//       title: 'MIC折点计算',
//       icon: '🧫',
//       content: `提供专业的MIC折点查询功能:
// • 支持关键字模糊菌株输入或菌株代码,比如输入肠球菌，化脓等两个字以上关键字后或一个字按空格键,或菌株代码比如aba, aha,enh 即可生成联动信息
// • 系统提供历史搜索数据用户可以在快捷查询中找到常用的搜索记录,点击搜索即可完成新的mic数据。
// • 快捷查询中找到常用的搜索记录,点击搜索前,用户应检查是否是已经列出的项目以防止重复查询,或建议在快捷查询前,点击清屏按钮清楚历史数据后再完成新的mic数据。
// • 点击"生成报告" 系统弹出对话框，用户选择采用的标本编号和使用的权威机构标准后点击生成报表，如果系统信息没有包括具体机构标准则输出CLSI标准
// • 如果出现找不到对应的mic折点表的情况,可自己在医药词典板块自行添加
// • 支持CLSI、EUCAST、FDA等国际标准
// • 输入细菌名称和药物浓度
// • 自动计算敏感性结果
// • 提供敏感、中介、耐药判断`
//     },
    {
      id: 'mic-test',
      title: '用药指导',
      icon: '🧫',
      content: `提供专业的MIC折点查询功能:
• 从下拉框中选择患者的检验方法，系统列出所有的可用的抗菌药物并自动屏蔽了天然耐药。
• 系统提供历史搜索数据用户可以在快捷查询中找到常用的搜索记录,点击搜索即可完成新的mic数据。
• 快捷查询中找到常用的搜索记录,点击搜索前,用户应检查是否是已经列出的项目以防止重复查询,或建议在快捷查询前,点击清屏按钮清楚历史数据后再完成新的mic数据。
• 点击"生成报告" 系统弹出对话框，用户选择采用的标本编号和使用的权威机构标准后点击生成报表，如果系统信息没有包括具体机构标准则输出CLSI标准
• 如果出现找不到对应的mic折点表的情况,可自己在医药词典板块自行添加
• 支持CLSI、EUCAST、FDA等国际标准
• 输入细菌名称和药物浓度
• 自动计算敏感性结果
• 提供敏感、中介、耐药判断`
    },
//     {
//       id: 'sample-management',
//       title: '实验样本管理',
//       icon: '🔬',
//       content: `全面的样本管理系统：
// • 记录涂片结果
// • 培养方法记录
// • 实验结论录入
// • 样本追踪和查询`
//     },
    {
      id: 'report-generation',
      title: '自动生成报告',
      icon: '📄',
      content: `智能报告生成系统：
• 整合患者信息和实验结果
• 生成标准化医学报告
• 支持报告打印
• 多种格式导出选项`
    },
    {
      id: 'medical-dictionary',
      title: '医药词典和数据字典',
      icon: '📚',
      content: `专业医药参考工具：
• 菌株类别增删改,选中父类项目后可以添加子类内容,选中任意项目可以编辑或删除
• 抗菌药物类别索引,可以按照抗生素代码进行检索
• 药敏试验折点按照检测方法分为MIC,纸片法和MIC+纸片法，用户使用前，首先从下拉列表中选择要采用的检测方法。
• 提供数据导入功能，导入数据前点击下载按钮，下载上传模板,导入数据中的bacterId,可以通过点击添加新行，输入细菌名称,细菌名称务必和菌株类别中的名称相同,否则无法正确添加.系统会在屏幕上显示细菌的id.
• 药敏试验折点提供历史数据查询和编辑功能，点击导入按钮可导入历史记录进行修改后保存
• WHONET模块实现了WHONET官网提供的iLabLink数据接,用户可以手动添加数据，也可以从历史文件.txt 文件或csv文件中导入,再添加用户自己新建的数据。
• WHONET模块点击导出csv后就可以生成接口标准文档。
• 实时更新的数据库`
    }
  ];

  const handleSectionToggle = (sectionId) => {
    setExpandedSectionId(expandedSectionId === sectionId ? null : sectionId);
  };

  return (
    <div className="help-page">
      <div className="help-header">
        <h1 style={{color:'white'}}>药敏专家系统使用帮助</h1>
        <p>欢迎使用药敏专家系统，以下是主要功能的详细说明</p>
      </div>
      
      <div className="help-sections">
        {sections.map(section => (
          <HelpSection
            key={section.id}
            section={section}
            isExpanded={expandedSectionId === section.id}
            onToggle={() => handleSectionToggle(section.id)}
          />
        ))}
      </div>

      <HelpFooter />
    </div>
  );
};

export default HelpPage;