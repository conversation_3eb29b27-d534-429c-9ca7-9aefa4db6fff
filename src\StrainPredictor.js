import { useState } from 'react';
import { useSharedState } from './SharedStateContext';

const styles = {
  container: {
    maxWidth: '800px',
    minWidth:'450px',
    margin: '40px auto',
    padding: '20px',
    backgroundColor: '#ffffff',
    borderRadius: '8px',
    boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
    boxSizing: 'border-box', // 添加这行确保padding计入总宽度
  },
  title: {
    fontSize: '24px',
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: '24px',
    color: '#333',
  },
  formContainer: {
    width: '100%',
    maxWidth: '600px', // 限制表单最大宽度
    margin: '0 auto', // 居中表单
  },
  formGroup: {
    marginBottom: '16px',
    width: '100%', // 确保formGroup占满容器宽度
  },
  label: {
    display: 'block',
    marginBottom: '8px',
    color: '#374151',
    fontWeight: '500',
    fontSize:'14px',
  },
  input: {
    width: '100%', // 设置输入框宽度为100%
    padding: '10px 16px',
    border: '1px solid #ddd',
    borderRadius: '4px',
    fontSize: '16px',
    outline: 'none',
    transition: 'border-color 0.2s',
    boxSizing: 'border-box', // 确保padding不会增加总宽度
  },
  select: {
    width: '100%',
    padding: '10px 16px',
    border: '1px solid #ddd',
    borderRadius: '4px',
    fontSize: '14px',
    outline: 'none',
    backgroundColor: 'white',
    cursor: 'pointer',
    boxSizing: 'border-box',

  },
  button: {
    padding: '10px 24px',
    backgroundColor: '#3b82f6',
    color: 'white',
    border: 'none',
    borderRadius: '4px',
    fontSize: '16px',
    cursor: 'pointer',
    transition: 'background-color 0.2s',
    marginTop: '24px',
    width: '100%',
  },
  buttonDisabled: {
    backgroundColor: '#ccc',
    cursor: 'not-allowed',
  },
  buttonHover: {
    backgroundColor: '#2563eb',
  },
  table: {
    width: '100%',
    borderCollapse: 'collapse',
    marginTop: '20px',
    backgroundColor: 'white',
    borderRadius: '8px',
    overflow: 'hidden',
    border: '1px solid #e5e7eb',
  },
  tableHeader: {
    backgroundColor: '#f9fafb',
    borderBottom: '1px solid #e5e7eb',
  },
  th: {
    padding: '12px 16px',
    textAlign: 'left',
    fontSize: '14px',
    fontWeight: '600',
    color: '#374151',
  },
  td: {
    padding: '12px 16px',
    borderBottom: '1px solid #e5e7eb',
    color: '#4b5563',
    fontSize:'14px',
    align:'center',
  },
  row: {
    transition: 'background-color 0.2s',
  },
  rowHover: {
    backgroundColor: '#f9fafb',
  },
};

const StrainPredictor = () => {
  const [inputStrain, setInputStrain] = useState('');
  const [testType, setTestType] = useState('');
  const [testBacteria, setTestBacteria] = useState('');
  const [predictions, setPredictions] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [hoveredRow, setHoveredRow] = useState(null);
  const [isButtonHovered, setIsButtonHovered] = useState(false);
  const { state, dispatch } = useSharedState();
  const bacteria_names = ['aba', 'ac-', 'acv', 'acx', 'aeh', 'aer', 'ajo', 'aju', 'alc', 'ave', 'axy', 'bca',
'buk', 'cfr', 'chb', 'ci-', 'com', 'cyo', 'eae', 'eas', 'eav', 'ecl', 'eco', 'efa',
'efm', 'ega', 'era', 'fgl', 'fin', 'fme', 'fod', 'gc1', 'ha-', 'hin', 'hpi', 'kor',
'kox', 'kpn', 'lmo', 'mmo', 'pae', 'pce', 'pma', 'pmi', 'ppu', 'pr-', 'pre', 'ps-',
'pst', 'pvu', 'ral', 'sal', 'san', 'sau', 'sca', 'scn', 'scp', 'sct', 'sdy', 'sep',
'sgc', 'sgo', 'sgy', 'shl', 'sho', 'slq', 'sma', 'smt', 'smu', 'sol', 'spn', 'spy',
'ss1', 'ssa', 'str', 'sve'];
const testCodes = ['ur', 'cv', 'ab', 're', 'bi', 'su', 'fl', 'dr', 'wd', 'ti',
  'bl', 'se', 'ot', 'as', 'sf', 'ps', 'ca', 'pf', 'ba', 'uc',
  'sp', 'sm', 'sk', 'sb', 'ea', 'th', 'jf', 'pa', 'no', 'va',
  'bm', 'ga', 'ac', 'np', 'st', 'pi', 'cy', 'ey', 'us'
];
const bacteriaCodeToChinese = {
  'aba': '鲍曼不动杆菌',
  'ac-': 'AC-菌属',
  'acv': 'ACV芽孢杆菌',
  'acx': 'ACX菌',
  'aeh': 'AEH嗜血杆菌',
  'aer': '产气肠杆菌',
  'ajo': 'AJ橄榄菌',
  'aju': 'AJ乌拉菌',
  'alc': '产碱杆菌',
  'ave': 'AVE链霉菌',
  'axy': 'AXY放线菌',
  'bca': '芽孢杆菌属',
  'buk': 'BUK菌株',
  'cfr': '耐甲氧西林菌',
  'chb': 'CHB弧菌',
  'ci-': 'CI-菌种',
  'com': 'COM共生菌',
  'cyo': 'CYO蓝细菌',
  'eae': '肠集聚性大肠杆菌',
  'eas': 'EAS菌株',
  'eav': 'EAV菌株',
  'ecl': '阴沟肠杆菌',
  'eco': '大肠埃希氏菌',
  'efa': '粪肠球菌',
  'efm': '屎肠球菌',
  'ega': 'EGA菌株',
  'era': 'ERA红螺菌',
  'fgl': 'FGL真菌',
  'fin': 'FIN菌株',
  'fme': 'FME菌株',
  'fod': 'FOD菌株',
  'gc1': 'GC1菌株',
  'ha-': 'HA-嗜血菌',
  'hin': '流感嗜血杆菌',
  'hpi': 'HPI菌株',
  'kor': 'KOR菌株',
  'kox': '肺炎克雷伯菌',
  'kpn': '克雷伯菌属',
  'lmo': '单核细胞增生李斯特菌',
  'mmo': '甲烷氧化菌',
  'pae': '铜绿假单胞菌',
  'pce': 'PCE降解菌',
  'pma': 'PMA菌株',
  'pmi': 'PMI菌株',
  'ppu': '恶臭假单胞菌',
  'pr-': 'PR-菌株',
  'pre': 'PRE菌株',
  'ps-': 'PS-假单胞菌',
  'pst': '丁香假单胞菌',
  'pvu': 'PVU菌株',
  'ral': 'RAL菌株',
  'sal': '沙门氏菌属',
  'san': 'SAN菌株',
  'sau': '金黄色葡萄球菌',
  'sca': 'SCA链球菌',
  'scn': 'SCN菌株',
  'scp': 'SCP菌株',
  'sct': 'SCT菌株',
  'sdy': 'SDY菌株',
  'sep': 'SEP菌株',
  'sgc': 'SGC菌株',
  'sgo': 'SGO菌株',
  'sgy': 'SGY菌株',
  'shl': 'SHL菌株',
  'sho': '志贺氏菌',
  'slq': 'SLQ菌株',
  'sma': 'SMA菌株',
  'smt': 'SMT菌株',
  'smu': '变异链球菌',
  'sol': 'SOL菌株',
  'spn': '肺炎链球菌',
  'spy': '化脓性链球菌',
  'ss1': 'SS1菌株',
  'ssa': 'SSA菌株',
  'str': '链霉菌属',
  'sve': 'SVE菌株'
};

const testMethodMap = {
  ur: '尿常规检测',
  cv: '心血管检测',
  ab: '抗体检测',
  re: '耐药性检测',
  bi: '生物标志物检测',
  su: '物质筛查',
  fl: '荧光检测',
  dr: '药物残留检测',
  wd: '水质检测',
  ti: '组织活检',
  bl: '血液检测',
  se: '血清学检测',
  ot: '其他检测',
  as: '过敏原筛查',
  sf: '食品安全检测',
  ps: '病原体筛查',
  ca: '癌症筛查',
  pf: '蛋白质功能检测',
  ba: '细菌培养检测',
  uc: '尿液细胞学检测',
  sp: '物种鉴定',
  sm: '代谢物检测',
  sk: '皮肤检测',
  sb: '血清生化检测',
  ea: '环境空气检测',
  th: '毒理学检测',
  jf: '基因分型检测',
  pa: '病理分析',
  no: '核酸检测',
  va: '疫苗效价检测',
  bm: '生物力学检测',
  ga: '基因测序分析',
  ac: '活性成分检测',
  np: '纳米颗粒检测',
  st: '应激测试',
  pi: '病原体免疫检测',
  cy: '细胞学检测',
  ey: '眼部检测',
  us: '超声检测'
};



const predictStrains = async () => {
  setIsLoading(true);

  try {
    if (!state.patient) return;
    if(!testType) return;
    if(!testBacteria) return;
    const inputStrain = {
      "AGE": state.patient.age,
      "SEX": state.patient.gender === "男" ? 'm' : 'f', 
      "WARD_TYPE": state.patient.ward, 
      "SPEC_TYPE": testType,
      "CURRENT_ORGANISM": testBacteria,
    };

   
    const response = await fetch('http://************:5281/predict', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(inputStrain),

    });

    if (!response.ok) {
      throw new Error('Failed to fetch predictions');
    }
   
   
    const data = await response.json();
   
    // Assuming that the API returns an object with bacteria strains and their probabilities
    const apiPredictions = Object.entries(data["Prediction Probabilities"])
  .map(([strain, similarity]) => ({
    strain,
    similarity: similarity.toFixed(2), // Ensuring it's formatted as a string with two decimal places
  }))
  .sort((a, b) => parseFloat(b.similarity) - parseFloat(a.similarity)); // Sorting by similarity

setPredictions(apiPredictions);

   
  } catch (error) {
    console.error('Error fetching predictions:', error);
    setPredictions([]);
  } finally {
    setIsLoading(false);
  }
};

  return (
    <div style={styles.container}>
       <h6 style={{ textAlign: 'center', margin: 10, color: 'blue',fontSize:'16px' }}> AI 预测<br/> {state.patient ? '当前执行患者：' + state.patient.name : '还没有选择患者'}</h6>
       <div style={styles.formGroup}>
          {/* <select 
            value={testType}
            onChange={(e) => setTestType(e.target.value)}
            style={styles.select}
          >
            <option value="">请选择化验类型代码</option>
            {testCodes.map(type => (
              <option key={type} >
                {type}
              </option>
            ))}
          </select> */}

          <select
            value={testType}
            onChange={(e) => setTestType(e.target.value) }
            style={styles.select}
          >
            <option value="">请选择检测方法</option>
            {Object.entries(testMethodMap).map(([code, name]) => (
              <option key={code} value={code}>
                {name}
              </option>
            ))}
          </select>
      </div>
     
      
      <div style={styles.formContainer}>
       
        <div style={styles.formGroup}>
        <select 
            value={testBacteria}
            onChange={(e) => setTestBacteria(e.target.value)}
            style={styles.select}
          >
            <option value="">请选择第一株菌株代码</option>
            {/* 直接遍历键值对对象 */}
            {Object.entries(bacteriaCodeToChinese).map(([code, name]) => (
              <option 
                key={code}       // 唯一标识用代码（如 'eco'）
                value={code}     // 提交值用代码（如 'eco'）
              >
                {name}           {/* 显示中文名称（如 '大肠埃希氏菌'） */}
              </option>
            ))}
          </select>
        </div>

        <button
          onClick={predictStrains}
         
          style={{
            ...styles.button,
           
          }}
          onMouseEnter={() => setIsButtonHovered(true)}
          onMouseLeave={() => setIsButtonHovered(false)}
        >
          {isLoading ? '预测中...' : 'AI预测'}
        </button>
      </div>

      {predictions.length > 0 && (
  <table style={styles.table}>
    <thead style={styles.tableHeader}>
      <tr>
        <th style={styles.th}>菌株</th>
        <th style={styles.th}>占比</th>
        {/* <th style={styles.th}>耐菌类型</th> */}
      </tr>
    </thead>
    <tbody>
      {/* 使用 slice(0, 3) 截取前 3 条数据 */}
      {predictions.slice(0, 3).map((prediction, index) => (
        <tr
          key={index}
          style={{
            ...styles.row,
            ...(hoveredRow === index ? styles.rowHover : {}),
          }}
          onMouseEnter={() => setHoveredRow(index)}
          onMouseLeave={() => setHoveredRow(null)}
        >
          <td style={styles.td}>{bacteriaCodeToChinese[prediction.strain] || prediction.strain}</td>
          <td style={styles.td}>{prediction.similarity}</td>
          {/* <td style={styles.td}>{prediction.orgType}</td> */}
        </tr>
      ))}
    </tbody>
  </table>
)}
    </div>

  );
};

export default StrainPredictor;