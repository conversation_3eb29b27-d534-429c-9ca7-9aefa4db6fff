import React, { useState,useEffect } from 'react';
import { Upload, Download,Trash2 } from "lucide-react";
import CustomDialog from './componets/CustomDialog';
import Breaking from './models/Breaking';
import * as XLSX from "xlsx";
import './EditableTable.css';
const EditableTable = () => {
  const [dialogVisible, setDialogVisible] = useState(false);
  const [hintMessage,setHintMessage] = useState('');
  const [data, setData] = useState([]);
  const [sendData, setSendData] = useState([]);
  const [selectedMethod, setSelectedMethod] = useState("MIC"); 
  const [newId, setNewId] = useState('');
  const [currentBacteria, setCurrentBacteria] = useState('');
  const BASE_URL = process.env.REACT_APP_BASE_URL;
  useEffect(() => {
   
  }, [data]);

  const handleClose = () => {
    setDialogVisible(false);
  };

  const handleImport = (e) => {
    
    const file = e.target.files[0]; // 获取上传的文件
    
    if (!file) return;

    const reader = new FileReader();
   
    // 当文件加载完毕时
    reader.onload = (event) => {
      const binaryStr = event.target.result;
      const workbook = XLSX.read(binaryStr, { type: "binary" });

      const sheetName = workbook.SheetNames[0];
      
      const worksheet = workbook.Sheets[sheetName];

      // 将工作表的数据转为 JSON 格式
      const jsonData = XLSX.utils.sheet_to_json(worksheet);
     
      // 为每条记录增加序号，从当前 data 长度继续编号
      
      const newData = jsonData.map((row, index) => ({
        id: index + 1, // 自动生成序号
        sensitivityMethod: selectedMethod,
        ...row,
      }));
     // 追加新数据
      setData((prevData) => [...prevData, ...newData]);
      // 清空文件输入值
      e.target.value = null;
    };

    reader.readAsBinaryString(file);
  };

  const handleExport = () => {
    
    const templateUrl = `${process.env.PUBLIC_URL + "template.xltx"}`;

    // 创建一个临时 <a> 标签
    const link = document.createElement("a");
    link.href = templateUrl; // 指向模板路径
    link.download = "mic_custom_template.xltx" 
    document.body.appendChild(link);

    // 触发下载
    link.click();

    // 清理临时元素
    document.body.removeChild(link);
  };


  const handleInputChange = (e, index, key) => {
    const updatedData = [...data];
    updatedData[index][key] = e.target.value;
    setData(updatedData);
  };
  const handleAddRow = () => {

    const newRow = new Breaking({
      id: `${data.length + 1}`, 
     sensitivityMethod: selectedMethod, // Use selected method
    });
     setData((prevData) => [...prevData, newRow]);
     setCurrentBacteria('');
  };
  

  const handleMethodChange = (id,value) => {
  
     if (data.length > 0) {
        setHintMessage("请先保存文档或删除");
        setDialogVisible(true);
        return;
      }
    
    setSelectedMethod(value);
     setData((prevData) =>
      prevData.map((row) =>
        row.id === id
          ? {
              ...row,
              sensitivityMethod: value,
              bacteriaType:"",
              antibioticCode: "", // 切换方法时清空抗生素代码
            }
          : row
      )
    );
  };
  
  const handleFieldChange =async (id, field, value) => {
    if (field === "antibioticCode") 
    {
      if(value && value.length==3){
        value=value.toUpperCase();
        const agentName = await handleQuery(value);
        const bactera_id = await handleBactery(currentBacteria);
        setData((prevData) =>
        prevData.map((row) =>
          row.id === id
            ? { ...row, [field]: value, antimicrobialAgent: agentName,bacterId: bactera_id }
            : row
        )
      );
      }
    }
    
    if (field === "bacteriaName") 
    {
       setCurrentBacteria(value);
      
    }
    
     setData((prevData) =>
      prevData.map((row) =>
        row.id === id ? { ...row, [field]: value } : row
      )
    );
  };
  
  
  const getDynamicHeaders = (method) => {
    switch (selectedMethod) {
      case "MIC":
        return ["S(μg/mL)", "SDD(μg/mL)","I (μg/mL)","R (μg/mL)"];
      case "Disk":
        return ["纸片含量(mm)","S(mm)", "SDD(mm)","I(mm)", "R(mm)"];
      case "MIC+Disk":
        return ["纸片含量(mm)", "S(mm)", "SDD(mm)","I(mm)", "R(mm)","S(μg/mL)", "SDD(μg/mL)","I (μg/mL)","R (μg/mL)"];
      default:
        return [];
    }
  };

  const handleDeleteRow = (id) => {
    const updatedData = data.filter((row) => row.id !== id);
    setData(updatedData);
   
  };

  const fetchData = async (newId) => {
    try {
     
      const response = await fetch(`${BASE_URL}api/get-data/${newId}`);
      if (response.ok) {
        const result = await response.json();
        return result.data;
      } else {
        const error = await response.json();
        console.error('Error:', error.message);
        return [];
      }
    } catch (error) {
      console.error('Error:', error);
      return [];
    }
  };
  const handleLoadData = async () => {
    if (!newId) {
      setHintMessage("编号不能为空");
      setDialogVisible(true);
      return;
    }
  
    // Reset table before loading new data
    setData([]);
    //setSelectedMethod("MIC+Disk");
  
    const loadedData = await fetchData(newId);
  
    if (loadedData && loadedData.length > 0) {
      const formattedData = loadedData.map((item, index) => {
        return new Breaking({
          id: index + 1, // Ensure unique IDs for each row
          antibioticCode: item["antibioticCode"] || "-", // Default empty if not available
          antimicrobialAgent: item["Antimicrobial Agent"] || "-",
          diskContent: item["Disk Content"] || "-",
          s_mm: item["S (mm)"] || "-",
          sdd_mm: item["SDD (mm)"] || "-",
          i_mm: item["I (mm)"] || "-",
          r_mm: item["R (mm)"] || "-",
          s_ug: item["S (μg/mL)"] || "-",
          sdd_ug: item["SDD (μg/mL)"] || "-",
          i_ug: item["I (μg/mL)"] || "-",
          r_ug: item["R (μg/mL)"] || "-",
          bacterId: item["BacterId"] || "-",
          bacteriaName: item["bacteriaName"] || "-", // Adapted for compatibility
          comments: item["Comments"] || "-",
          newId: item["newId"] || "-",
          sensitivityMethod: selectedMethod,
          nickName: item["nickName"] || "-",
        });
      });
  
      setData(formattedData);
      
    } else {
      setHintMessage("没有找到对应的信息");
      setDialogVisible(true);
    }
  };
  
  
  const validateData = (method, data) => {
    return data.every((row) => {
      switch (method) {
        case "MIC":
          return row.antimicrobialAgent && row.bacteriaName && row.s_ug && row.r_ug;
        case "Disk":
          return row.antimicrobialAgent && row.bacteriaName && row.diskContent && row.s_mm  && row.r_mm;
        case "MIC+Disk":
          return row.antimicrobialAgent && row.bacteriaName && row.diskContent && row.s_mm  && row.r_mm && row.s_ug && row.r_ug;
        default:
          return false; 
      }
    });
  };


  const handleQuery = async (code) => {
    try {
    
      const response = await fetch(`${BASE_URL}antibiotics/${code.toUpperCase()}`);
      
      if (response.ok) {
        const data = await response.json();
        const agentName = data?.['DISPLAY_NAME'] || "";
        return agentName;     
      } else {
        console.log('未找到相关数据');
      }

    } catch (error) {
      console.log('查询失败：' + (error.response?.data?.message || '未找到该抗生素'));
    } finally {
      
    }
  };

  
  const handleBactery = async (bacterName) => {
    try {
      let response = await fetch(`${BASE_URL}api/suggestions?query=${bacterName}`);
      if (!response.ok) {
        throw new Error('Network re sponse was not ok');
      }
      const data = await response.json();
      console.log(data)
      if(data && data.length>0){
      const bactera_id = data[0].parent_id;
      return (bactera_id);
      }
      return null; 
      
      } catch (error) {
          console.error('Error fetching suggestions:', error);
        }
  };

  
  const handleRemove =async() =>{
    
      const isConfirmed = window.confirm(` ${newId}? 批次数据被清除?`);
      if (isConfirmed){
        const response = await fetch(`${BASE_URL}api/delete-data/${newId}`, {
          method: 'DELETE',
        });
        if (response.ok) {
          const data = await response.json();
          setNewId('');
          setHintMessage("提交成功");
          setDialogVisible(true);
        
          return;
       
        } else {
          setHintMessage('没有找到对应的信息');
           setDialogVisible(true);
           return;
        }

      }
     
   
  };

  const handleSave = async () => {
    if(!data) return;
    if(data.length==0 && newId)
    handleRemove();

    if(data && data.length>0){
       
       if(!newId){
        setHintMessage("输入批次编号");
        setDialogVisible(true);
        return;
       }

      
       const isValid = validateData(selectedMethod, data);
        if (!isValid) {
          setHintMessage(`Please ensure all fields are filled for the ${selectedMethod} method.`);
          setDialogVisible(true);
          return;
        }


        const sendData = data.map((item) =>{
          return {
            'Antimicrobial Agent': item.antimicrobialAgent,
            'Disk Content': item.diskContent,
            'S (mm)': item.s_mm,
            'SDD (mm)': item.sdd_mm,
            'I (mm)': item.i_mm,
            'R (mm)': item.r_mm,
            'S (μg/mL)': item.s_ug,
            'SDD (μg/mL)': item.sdd_ug,
            'I (μg/mL)': item.i_ug,
            'R (μg/mL)': item.r_ug,
            'BacterId': item.bacterId,
            'Comments': "SELF:" + item.comments,
            'newId': item.newId,
            'antibioticCode':item.antibioticCode,
            'bacteriaName': item.bacteriaName
           
          };
        }
       );
       
     

      const dataToSave={newId:newId, data:sendData}
     
        try {
          const response = await fetch(`${BASE_URL}api/save-data`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(dataToSave)
          });
      
          if (response.ok) {
              setHintMessage("提交成功");
              setDialogVisible(true);
              setData([]);
              setNewId('');
              setCurrentBacteria('');
           
          } else {
            setHintMessage("错误,保存数据失败");
            setDialogVisible(true);
            console.error('Error saving data:', await response.json());
          }
        } catch (error) {
            setHintMessage("错误,保存数据失败");
            setDialogVisible(true);
          console.error('Error:', error);
        }
     
   }
  
  };

  return (
<div className="mscontainer">

<div className="title-container">
<h5>药敏试验折点 </h5>
  <div className="icons">
     <input
          type="file"
          accept=".xlsx, .xls"
          onChange={handleImport}
          style={{ display: "none" }}  // 隐藏原生的上传按钮
          id="file-input"
        />
    <button
          type="button"
          onClick={() => document.getElementById("file-input").click()}
          title="导入数据"
          style={{ background: "none", border: "none", cursor: "pointer" }}
        >
          <Upload size={20} />
        </button>

    {/* 下载按钮 */}
    <button  type="button" onClick={handleExport} title="下载模板" 
      style={{ background: "none", border: "none", cursor: "pointer" }}
    >
      <Download size={20} />
    </button>
  </div>

  
</div>

<div className="settings-container">
  <div className="inputs-row">
    <label style={{color:"white"}}>检测方法：</label>
    <select
      value={selectedMethod}
      onChange={(e) => handleMethodChange(0, e.target.value)}
    >
      <option value="MIC">MIC</option>
      <option value="Disk">纸片法</option>
      <option value="MIC+Disk">MIC+纸片法</option>
    </select>
    <input
      type="text"
      name="new_id"
      placeholder="输入归档编号"
      required
      value={newId}
      onChange={(e) => setNewId(e.target.value)}
    />
    <button type="button" className="mssave-btn" onClick={handleLoadData}>
      导入历史记录
    </button>
   

  </div>
  <small className="msform-note">已创建的信息可以使用编号导入后编辑</small>
</div>

    
<table border="1" className="result-table">
    <thead >

    <tr>
      <th>No.</th>
      <th>细菌名称</th>
      <th>抗生素代码</th>
      <th>抗生素名称</th>
      {getDynamicHeaders(selectedMethod).map((header, index) => (
        <th key={index}>{header}</th>
      ))}
      <th>备注</th>
      <th>操作</th>
    </tr>
    </thead>
    <tbody>
          {data.map((row) => (
            <tr key={row.id}>
              <td>{row.id}</td>
              {/* 药敏试验方法 */}
              
              <td>
                <input
                  type="text"
                  value={row.bacteriaName}
                  onChange={(e) =>
                    handleFieldChange(row.id, "bacteriaName", e.target.value)
                  }
                  
                  className="mic-input"
                />
              </td>
              
              {/* 抗生素代码 */}
              <td>
                <input
                  type="text"
                  value={row.antibioticCode}
                  onChange={(e) =>
                    handleFieldChange(row.id, "antibioticCode", e.target.value)
                  }
                 
                  className="mic-input"
                />
              </td>
              <td>
                <input
                  type="text"
                  value={row.antimicrobialAgent}
                  onChange={(e) =>
                    handleFieldChange(row.id, "antimicrobialAgent", e.target.value)
                  }
                 
                  className="mic-input"
                />
              </td>
              {/* 动态内容 */}
              {row.sensitivityMethod === "MIC" && (
                <>
                  <td>
                    <input
                      type="text"
                      value={row['s_ug']}
                      onChange={(e) =>
                        handleFieldChange(row.id, "s_ug", e.target.value)
                      }
                      
                      className="mic-input"
                    />
                  </td>
                  <td>
                    <input
                      type="text"
                      value={row['sdd_ug']}
                      onChange={(e) =>
                        handleFieldChange(row.id, "sdd_ug", e.target.value)
                      }
                    
                      className="mic-input"
                    />
                  </td>
                  <td>
                    <input
                      type="text"
                      value={row['i_ug']}
                      onChange={(e) =>
                        handleFieldChange(row.id, "i_ug", e.target.value)
                      }
                    
                      className="mic-input"
                    />
                  </td>
                  <td>
                    <input
                      type="text"
                      value={row['r_ug']}
                      onChange={(e) =>
                        handleFieldChange(row.id, "r_ug", e.target.value)
                      }
                     
                      className="mic-input"
                    />
                  </td>
                </>
              )}
              {row.sensitivityMethod === "Disk" && (
                <>
                  <td>
                    <input
                      type="text"
                      value={row.diskContent}
                      onChange={(e) =>
                        handleFieldChange(row.id, "diskContent", e.target.value)
                      }
                     
                      className="mic-input"
                    />
                  </td>
                  <td>
                    <input
                      type="text"
                      value={row.s_mm}
                      onChange={(e) =>
                        handleFieldChange(row.id, "s_mm", e.target.value)
                      }
                    
                      className="mic-input"
                    />
                  </td>
                  <td>
                    <input
                      type="text"
                      value={row.sdd_mm }
                      onChange={(e) =>
                        handleFieldChange(row.id, "sdd_mm", e.target.value)
                      }
                     
                      className="mic-input"
                    />
                  </td>
                  <td>
                    <input
                      type="text"
                      value={row.i_mm }
                      onChange={(e) =>
                        handleFieldChange(row.id, "i_mm", e.target.value)
                      }
                      
                      className="mic-input"
                    />
                  </td>
                  <td>
                    <input
                      type="text"
                      value={row.r_mm}
                      onChange={(e) =>
                        handleFieldChange(row.id, "r_mm", e.target.value)
                      }
                     
                      className="mic-input"
                    />
                  </td>
                </>
              )}
              {row.sensitivityMethod === "MIC+Disk" && (
                <>
                  
                  <td>
                    <input
                      type="text"
                      value={row.diskContent}
                      onChange={(e) =>
                        handleFieldChange(row.id, "diskContent", e.target.value)
                      }
                     
                      className="mic-input"
                    />
                  </td>
                  <td>
                    <input
                      type="text"
                      value={row.s_mm}
                      onChange={(e) =>
                        handleFieldChange(row.id, "s_mm", e.target.value)
                      }
                     
                      className="mic-input"
                    />
                  </td>
                  <td>
                    <input
                      type="text"
                      value={row.sdd_mm }
                      onChange={(e) =>
                        handleFieldChange(row.id, "sdd_mm", e.target.value)
                      }
                     
                      className="mic-input"
                    />
                  </td>
                  <td>
                    <input
                      type="text"
                      value={row.i_mm }
                      onChange={(e) =>
                        handleFieldChange(row.id, "i_mm", e.target.value)
                      }
                     
                      className="mic-input"
                    />
                  </td>
                  <td>
                    <input
                      type="text"
                      value={row. r_mm }
                      onChange={(e) =>
                        handleFieldChange(row.id, "r_mm", e.target.value)
                      }
                    
                      className="mic-input"
                    />
                  </td>
                  <td>
                    <input
                      type="text"
                      value={row['s_ug']}
                      onChange={(e) =>
                        handleFieldChange(row.id, "s_ug", e.target.value)
                      }
                     
                      className="mic-input"
                    />
                  </td>
                  <td>
                    <input
                      type="text"
                      value={row['sdd_ug']}
                      onChange={(e) =>
                        handleFieldChange(row.id, "sdd_ug", e.target.value)
                      }
                     
                      className="mic-input"
                    />
                  </td>
                  <td>
                    <input
                      type="text"
                      value={row['i_ug']}
                      onChange={(e) =>
                        handleFieldChange(row.id, "i_ug", e.target.value)
                      }
                     
                      className="mic-input"
                    />
                  </td>
                  <td>
                    <input
                      type="text"
                      value={row['r_ug']}
                      onChange={(e) =>
                        handleFieldChange(row.id, "r_ug", e.target.value)
                      }
                     
                      className="mic-input"
                    />
                  </td>
                </>
              )}
              {/* 备注 */}
              <td>
                <input
                  type="text"
                  value={row.comments}
                  onChange={(e) =>
                    handleFieldChange(row.id, "comments", e.target.value)
                  }
                 
                  className="mic-input"
                />
              </td>
              {/* 操作 */}
              <td>
                <button style={{background:"white"}} onClick={() => handleDeleteRow(row.id)}>
                 <Trash2 size={20}  style={{color:"black"}}/>

                </button>
              </td>
            </tr>
          ))}
        </tbody>
  </table>
      <div className="table-footer">
        <button className="add-btn" onClick={handleAddRow}>
          添加新行
        </button>
        <button className="save-btn" onClick={handleSave}>
          保存修改
        </button>
      </div>
      {dialogVisible && (
          <CustomDialog
            message={hintMessage}
            onClose={handleClose}
          />
        )}
    </div>
  );
};

export default EditableTable;
