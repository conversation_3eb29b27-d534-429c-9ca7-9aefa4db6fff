import React, { useState,useEffect,useContext } from "react";
import { Tooltip } from 'antd';
import Patient from './models/PatientData';
import TestData from './models/TestData';
import { useSharedState } from './SharedStateContext';
import CustomDialog from './componets/CustomDialog';
import { FolderInput } from 'lucide-react';
import './QueryForm.css';
import TestDataModal from './componets/ui/TestDataModal';
import { RefreshCw } from 'lucide-react';
import { antibioticsDataStore } from './models/AntibioticsDataStore';
import { resultsDataStore } from './models/ResultsDataStore'

const PatientForm = ({ handleNavigation }) => {
  const [dialogVisible, setDialogVisible] = useState(false);
  const [formData, setFormData] = useState(new Patient());
  const [hintMessage,setHintMessage] = useState('');
  const [testMethod,setTestMethod] = useState('');
  const [testSupplement,setTestSupplement] = useState('');
  const [testMethods,setTestMethods] = useState([]);
  const [selectedOption, setSelectedOption] = useState('');
  const [customResult, setCustomResult] = useState('');
  const [sensitivityOption, setSensitivityOption] = useState('');
  const [customSensitivityResult, setCustomSensitivityResult] = useState('');
  const [testResult,setTestResult] = useState('');
  const [error, setError] = useState('');
  const {state, dispatch } = useSharedState();
  const [isModalVisible, setIsModalVisible] = useState(false);
  const BASE_URL = process.env.REACT_APP_BASE_URL;
  
useEffect(() => {
  if(state.patient)
   setFormData(state.patient);
}, [state.Patient]);



useEffect(() => {
  
   if(formData.sampleCode && formData.sampleCode.length>=2){
     handleFetch()
   }
      
    

}, [formData.sampleCode]);


const handleRefresh = () => {
    const updatedFormData = Object.assign(Object.create(Object.getPrototypeOf(formData)), formData);
    updatedFormData['specimenNumber'] = generateTimestamp();
    setFormData(updatedFormData);
};


const generateTimestamp = () => {
    const now = new Date();
    return now.getFullYear().toString() +
           String(now.getMonth() + 1).padStart(2, '0') +
           String(now.getDate()).padStart(2, '0') +
           String(now.getHours()).padStart(2, '0') +
           String(now.getMinutes()).padStart(2, '0') +
           String(now.getSeconds()).padStart(2, '0');
  };

    const handleShowModal = () => {
      setIsModalVisible(true);
    };
    const handleCloseModal = () => {
      setIsModalVisible(false);
    };
  
    const handleDelete = (index) => {
     formData.removeTestData(index); 
     const updatedPatient = Object.assign(new Patient(),formData);
     setFormData(updatedPatient);
    };
    
  const handleChange = (e) => {
    const { name, value } = e.target;
    const updatedFormData = Object.assign(Object.create(Object.getPrototypeOf(formData)), formData);
    updatedFormData[name] = value;
    setFormData(updatedFormData);
   
  };
  
  const handleSelectChange = (e) => {
    const value = e.target.value;
    setSelectedOption(value);
    if (value !== '其他') {
      setCustomResult(''); // 清空自定义输入
      setTestResult(value);
    }
  };
  
  const getFinalValue = () => {
   
    return selectedOption === '其他' ? customResult : selectedOption;
  };

  const getFinalSensitivityResult = () =>
    sensitivityOption === '其他' ? customSensitivityResult : sensitivityOption;

  const handleFetch = async () => {
    try {
      if (!formData.sampleCode.trim()) {
         setError('Please enter a code.');
         return;
      }
      
      const response = await fetch(`${BASE_URL}api/spec/${formData.sampleCode.toLowerCase()}`); 
  
      if(response.ok){
        const data = await response.json();
        const updatedFormData = Object.assign(Object.create(Object.getPrototypeOf(formData)), formData);
        updatedFormData['sampleType'] = data.chineseName;
        setFormData(updatedFormData);
        setError('');
      }
     
     
    } catch (err) {
      console.error('Error fetching data:', err);
      setError('An unexpected error occurred.');
    }
  };
  
  const createTestData=() =>{
  
   if(!formData.specimenNumber){
    setHintMessage("请输入标本编号");
    setDialogVisible(true);
    return;
   }
   if(!testMethod && !testSupplement){
    setHintMessage("请选择实验方法或输入补充实验方法");
    setDialogVisible(true);
    return;
   }

   if(testMethod && !getFinalValue()){
    setHintMessage("请选择解析结果");
    setDialogVisible(true);
    return;
   }
   if(testSupplement && !getFinalSensitivityResult()){
    setHintMessage("请选择补充解析结果");
    setDialogVisible(true);
    return;
   }


    
  
    const test1 = new TestData(testMethod,testSupplement,getFinalSensitivityResult(), getFinalValue());
    formData.addTestData(test1);
    setHintMessage("测试数据生成");
    setTestMethod('');
    setSelectedOption('');
    setSensitivityOption('');
    setCustomSensitivityResult('')
    setCustomResult('');
    setTestSupplement('');
    setTestResult('');

    setError('');
    
  }


  const handleClose = () => {
    setDialogVisible(false);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
  
    if(!formData.patient_id){
      setHintMessage("患者编号不能为空");
      setDialogVisible(true);

      return;
    }
    if(!formData.specimenNumber){
      setHintMessage("标本编号不能为空");
      setDialogVisible(true);

      return;
    }

    
    if(!formData.hospital_code){
      setHintMessage("患者医院编号不能为空");
      setDialogVisible(true);
      return;
    }
    if(!formData.name){
      setHintMessage("患者姓名不能为空");
      setDialogVisible(true);
      return;
    }
    // if (!formData.isValid()) {
    //   setHintMessage("请完善检测数据,所有检测数据不能为空");
    //   setDialogVisible(true);
    //   return;
    // }
  

    try {
      // 使用 POST 请求调用 saveOrUpdate API
      const response = await fetch(`${BASE_URL}patients`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });
  
      if (response.ok) {
        const result = await response.json();
        antibioticsDataStore.setData([]);
        resultsDataStore.setResults([]);
        
        // 分发保存到全局状态的操作
        dispatch({ type: 'SET_USER', payload: formData });
        handleNavigation('mic');
      } else {
        // 处理服务器错误
        setHintMessage("提交失败，请重试");
        setDialogVisible(true);
      }
    } catch (error) {
      // 处理网络错误或其他错误
      console.error('请求失败:', error);
      setHintMessage("网络错误，请稍后重试");
      setDialogVisible(true);
    }
  };
  
  const handleReset = () => {
    
    setFormData(new Patient());
    setError('');
    setTestMethod('');
    setTestResult('');
    setTestSupplement('');
    setCustomResult('');
    setCustomSensitivityResult('');
    setSelectedOption('');
    setSensitivityOption('');
    antibioticsDataStore.setData([]);
    resultsDataStore.setResults([]);
    dispatch({ type: 'SET_USER', payload: null });  
  };

  const deletePatientInfo = async () => {

    try {

      if(!formData.patient_id){
        setHintMessage("患者编号不能为空");
        setDialogVisible(true);
        return;
      }


      const response = await fetch(`${BASE_URL}api/patients/${formData.patient_id}`, {
        method: 'DELETE',
      });
  
      if (response.ok) {
        const data = await response.json();
        handleReset();
        setHintMessage(data.message);
        setDialogVisible(true);
     
      } else {
        const errorData = await response.json();
        
        console.error('Error:', errorData.message);
      }
    } catch (error) {
      console.error('Failed to delete patient info:', error);
    }
  };
  

  const fetchPatientInfo = async () => {
    try {
     
      const response = await fetch(`${BASE_URL}api/patients/${formData.specimenNumber}`);
      
      if (response.ok) {
       
        const fetchedData = await response.json();
        const updatedPatient = Object.assign(new Patient(), fetchedData.data);
        setFormData(updatedPatient);
       
       
      } else {
        // 如果未找到患者信息，显示提示
        setHintMessage("没有找到标本信息");
        setDialogVisible(true);
      }
    } catch (error) {
      // 如果请求失败，显示错误信息
     
      setHintMessage("获取标本信息失败，请重试");
      setDialogVisible(true);
    }
  };
  const fetchPatientBoard = async () => {
    try {
      
      const response = await fetch(`${BASE_URL}api/patients/consumable/${formData.consumableboardNumber}`);
      
      if (response.ok) {
       
        const fetchedData = await response.json();
        const updatedPatient = Object.assign(new Patient(), fetchedData.data);
        setFormData(updatedPatient);
       
       
      } else {
        // 如果未找到患者信息，显示提示
        setHintMessage("没有找到标本信息");
        setDialogVisible(true);
      }
    } catch (error) {
      // 如果请求失败，显示错误信息
     
      setHintMessage("获取标本信息失败，请重试");
      setDialogVisible(true);
    }
  };

  const fetchHstoryInf = async () => {
    try {
      const response = await fetch(`${BASE_URL}api/bacteria`);
      
      if (response.ok) {
       
        const fetchedData = await response.json();
        setTestMethods(fetchedData)
        // console.log(testMethods)
      
      } else {
        // 如果未找到患者信息，显示提示
        setHintMessage("没有找到历史数据");
        setDialogVisible(true);
      }
    } catch (error) {
      // 如果请求失败，显示错误信息
     
      setHintMessage("获取历史失败，请重试");
      setDialogVisible(true);
    }
  };
  
  // 自动根据培养结果（菌株名称）填写菌株代码
useEffect(() => {
  const fetchBacteriaCode = async () => {
    if (formData.cultureResult && formData.cultureResult.trim().length > 0) {
      try {
        const response = await fetch(`${BASE_URL}api/organism/code?name=${encodeURIComponent(formData.cultureResult.trim())}`);
        if (response.ok) {
          const data = await response.json();
          if (data && data.code) {
            const updatedFormData = Object.assign(Object.create(Object.getPrototypeOf(formData)), formData);
            updatedFormData.bacteriaCode = data.code;
            setFormData(updatedFormData);
          }
        }
      } catch (err) {
        // 可选：setError('自动获取菌株代码失败');
      }
    }
  };
  fetchBacteriaCode();
  // eslint-disable-next-line react-hooks/exhaustive-deps
}, [formData.cultureResult, BASE_URL]);

  return (
    <div className="container">
      <h6 style={{ textAlign: 'center', margin: 10, color: 'white' }}>标本信息录入</h6>
      <form className="query-form" onSubmit={handleSubmit}>
       
        <div className="form-group" style={{marginBottom:'10px'}}>
          <label className="form-label" >标本编号:</label>
          <input
          type="text"
          name="specimenNumber"
          value={formData.specimenNumber}
          onChange={handleChange}
          className="mic-input"
          placeholder="标本编号"
          required
        />
        <button
          type="button"
          onClick={handleRefresh}
          title="生成标本编号"
          className="import-button flex items-center justify-center p-2"
        >
          <RefreshCw className="w-4 h-4" size={20}/>
        </button>

        <button
          type="button"
          onClick={fetchPatientInfo}
          title="导入标本信息"
          className="import-button flex items-center justify-center p-2" 

          >
          <FolderInput size={20}  />
          </button>
        </div>
       
          <div className="form-group">
      <label className="form-label">耗材板编号: </label>
      
        <input
          type="text"
          name="consumableboardNumber"
          value={formData.consumableboardNumber}
          onChange={handleChange}
          className="mic-input"
          placeholder="输入耗材板编号"
         
        />
        <button
          type="button"
          onClick={fetchPatientBoard}
          title="输入耗材板编号"
          className="import-button flex items-center justify-center p-2" 

          >
          <FolderInput size={20}  />
          </button>
        
      </div>
       
          <div className="form-group">
      <label className="form-label">患者编号: </label>
      
        <input
          type="text"
          name="patient_id"
          value={formData.patient_id}
          onChange={handleChange}
          className="mic-input"
          placeholder="输入患者编号"
          required
        />
       
        
      </div>
   
      <div className="form-group">
      <label className="form-label">医院编号: </label>
      
        <input
          type="text"
          name="hospital_code"
          value={formData.hospital_code}
          onChange={handleChange}
          className="mic-input"
          placeholder="输入医院编号"
          required
        />
       
        
      </div>
   
    

        <div className="form-group">
          <label className="form-label">姓名: </label>
          <input
            type="text"
            name="name"
            value={formData.name}
            onChange={handleChange}
            className="mic-input"
            required
          />
        </div>
        
        <div className="form-group">
          <div className="radio-group">
            <label className="form-label">性别: </label>
            <label className="form-label">
              <input
                type="radio"
                name="gender"
                value="1"
                checked={formData.gender === "1"}
                onChange={handleChange}
              />
              男
            </label>
            <label className="form-label">
              <input
                type="radio"
                name="gender"
                value="0"
                checked={formData.gender === "0"}
                onChange={handleChange}
              />
              女
            </label>
          </div>
        </div>

        <div className="form-group">
          <label className="form-label">年龄: </label>
          <input
            type="text"
            name="age"
            value={formData.age}
            onChange={handleChange}
            className="mic-input"
            required
          />
        </div>

        <div className="form-group">
          <label className="form-label">科室: </label>
          <input
            type="text"
            name="department"
            value={formData.department}
            onChange={handleChange}
            className="mic-input"
            required
          />
        </div>

        <div className="form-group">
          <label className="form-label">病房: </label>
          <select
            name="ward"
            required
            value={formData.ward}
            onChange={handleChange}
            className="agent-select"
            style={{ width: "80%", height: "35px" }}
          >
            <option value="">请选择</option>
            <option value="in">住院</option>
            <option value="out">出院</option>
            <option value="eme">急诊</option>
            <option value="icu">ICU</option>
           
            </select>
        </div>

        <div className="form-group">
          <label className="form-label">病床: </label>
          <input
            type="text"
            name="bed"
            value={formData.bed}
            onChange={handleChange}
            className="mic-input"
          />
        </div>

        <div className="form-group">
          <label className="form-label">病历号: </label>
          <input
            type="text"
            name="medicalRecordNumber"
            value={formData.medicalRecordNumber}
            onChange={handleChange}
            className="mic-input"
          />
        </div>
        <div className="form-group" style={{marginBottom:'10px'}}>
          <label className="form-label" >标本代码: </label>
          <input
            type="text"
            name="sampleCode"
            value={formData.sampleCode}
            className="mic-input"
            placeholder="输入标本代码如ur,bl "
             onChange={handleChange}
          />
        </div>
        <div className="form-group" style={{marginBottom:'10px'}}>
          <label className="form-label" >标本类型: </label>
          <input
            type="text"
            name="sampleType"
            value={formData.sampleType}
            className="mic-input"
            onChange={handleChange}
            readOnly
          />
        </div>

        <div className="form-group" style={{marginBottom:'10px'}}>
          <label className="form-label" >涂片结果:</label>
          <input
            type="text"
            name="smearResult"
            value={formData.smearResult}
            onChange={handleChange}
            className="mic-input"
            required
          />
        </div>
        <div className="form-group" style={{marginBottom:'10px'}}>
          <label className="form-label" >菌落计数:</label>
          <input
            type="text"
            name="bacteriaCount"
            value={formData.bacteriaCount}
            onChange={handleChange}
            className="mic-input"
           
          />
        </div>

        <div className="form-group" style={{marginBottom:'10px'}}>
          <label className="form-label">培养结果:</label>
          <input
            type="text"
            name="cultureResult"
            value={formData.cultureResult}
            onChange={handleChange}
            className="mic-input"
            required
          
          />
          
        </div>
        <div className="form-group" style={{marginBottom:'10px'}}>
          <label className="form-label">菌株代码:</label>
          <input
            type="text"
            name="bacteriaCode"
            value={formData.bacteriaCode}
            onChange={handleChange}
            className="mic-input"
            required
          
          />
          
        </div>

        
 
    <div className="panel">
    <small className="form-note" >新建标本检测信息</small>
    
        <div className="form-group" style={{marginBottom:'10px'}}>
          <label className="form-label">实验方法:</label>
          <select
            name="testMethod"
            value={testMethod}
            onChange={(e) => setTestMethod(e.target.value)}
            className="agent-select"
            style={{ width: "77%", height: "35px" }}
          >
          <option value="">请选择</option>
          <option>EDTA-碳青霉烯灭活试验(eCIM)</option>
          <option>改良碳青霉烯灭活试验(mCIM)</option>
          <option>丝氨酸型碳青霉烯酶*</option>
          <option>肺炎克雷伯菌、产酸克雷伯菌、大肠埃希菌和奇异变形杆菌超广谱β-内酰胺酶试验</option>
          <option>肠杆菌目细菌和铜绿假单胞菌碳青霉烯酶试验</option>
          <option>疑似产碳青霉烯酶肠杆菌目细菌和铜绿假单胞菌CarbaNP试验</option>
          <option>疑似产碳青霉烯酶肠杆菌目细菌和铜绿假单胞菌改良碳青霉烯灭活试验</option>
          <option>氨曲南＋头孢他啶-阿维巴坦肉汤纸片洗脱法</option>
          <option>肠杆菌目细菌和铜绿假单胞菌黏菌素耐药性试验</option>
          <option>阳性血培养肉汤直接纸片扩散试验</option>
          <option>肠杆菌目细菌阳性血培养肉汤直接纸片扩散试验</option>
          <option>铜绿假单胞菌阳性血培养肉汤直接纸片扩散试验</option>
          <option>不动杆菌属阳性血培养肉汤直接纸片扩散试验</option>
          <option>葡萄球菌属β-内酰胺酶检测试验</option>
          <option>苯唑西林盐琼脂试验</option>
          <option>金黄色葡萄球菌和肠球菌属万古霉素琼脂筛选试验</option>
          <option>葡萄球菌属、肺炎链球菌和β-溶血性链球菌群诱导克林霉素耐药检测试验</option>
          <option>金黄色葡萄球菌高水平莫匹罗星耐药检测试验</option>
          <option>肠球菌属高水平氨基糖苷耐药检测试验</option>
          
          </select>
        </div>

        

        <div className="form-group" style={{marginBottom:'10px'}}>
          <label className="form-label">解析结果:</label>
          <select
        className="mic-input"
        value={selectedOption}
        onChange={handleSelectChange}
      >
        <option value="">请选择</option>
        <option value="阳性">阳性</option>
        <option value="阴性">阴性</option>
        <option value="其他">其他</option>
      </select>
      </div>
      {selectedOption === '其他' && (
         <div className="form-group" style={{marginBottom:'10px'}}>
           <label className="form-label">其他:</label>
        <input
          type="text"
          className="mic-input"
          placeholder="请输入其他结果"
          value={customResult}
          onChange={(e) => setCustomResult(e.target.value)}
          style={{ marginTop: '8px' }}
        />
        </div>
      )}

<div className="form-group" style={{marginBottom:'10px'}}>
           <label className="form-label">补充药敏试验:</label>
        <input
          type="text"
          className="mic-input"
          placeholder="请输入补充药敏试验"
          value={testSupplement}
          onChange={(e) => setTestSupplement(e.target.value)}
          style={{ marginTop: '8px' }}
        />
        </div>

      <div className="form-group" style={{marginBottom:'10px'}}>
          <label className="form-label" >补充解析结果: </label>
          <select
                className="mic-input"
                value={sensitivityOption}
                onChange={(e) => setSensitivityOption(e.target.value)}
              >
                <option value="">请选择</option>
                <option value="阳性">阳性</option>
                <option value="阴性">阴性</option>
                <option value="其他">其他</option>
              </select>
        </div>

        {sensitivityOption  === '其他' && (
         <div className="form-group" style={{marginBottom:'10px'}}>
           <label className="form-label">其他:</label>
        <input
          type="text"
          className="mic-input"
          placeholder="请输入其他结果"
          value={customSensitivityResult}
          onChange={(e) => setCustomSensitivityResult(e.target.value)}
          style={{ marginTop: '8px' }}
        />
        </div>
      )}
      
        <div className="submit-button-container">
        <button type="button" className="submit-button" onClick={createTestData} >生成检测数据</button>
          <button type="button" onClick={handleShowModal} className="view-button"  >查看检测数据</button>
        </div>

        </div>
      
        {error && <div style={{ color: 'red', marginBottom: '10px', textAlign: 'center', fontSize: '16px' }}>{error}</div>}
        <div className="submit-button-container">
          <button className="submit-button" type="submit">提交</button>
          <button type="button" className="reset-button" onClick={handleReset}>重置</button>
          <button type="button" onClick={deletePatientInfo}className="delete-button" >删除</button>
          
        </div>
        <TestDataModal
        visible={isModalVisible}
        onClose={handleCloseModal}
        testDataList={formData.getTestData()}
        onDelete={handleDelete}
       
      />
        {dialogVisible && (
          <CustomDialog
            message={hintMessage}
            onClose={handleClose}
          />
        )}
      </form>

     
      <div className="result-table-container">
      
   
      </div>
    </div>
  );
};

export default PatientForm;
