
.help-page {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
}

.help-header {
  text-align: center;
  margin-bottom: 40px;
}

.help-header h1 {
  font-size: 28px;
  color: #1a1a1a;
  margin-bottom: 12px;
}

.help-header p {
  color: #666;
  font-size: 16px;
}

.help-sections {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.help-section {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: #fff;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.help-section-header {
  width: 100%;
  padding: 16px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: none;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s;
}

.help-section-header:hover {
  background-color: #f9fafb;
}

.help-section-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 16px;
  font-weight: 500;
  color: #1a1a1a;
}

.icon {
  font-size: 20px;
}

.arrow {
  font-size: 12px;
  transition: transform 0.2s;
}

.help-section.expanded .arrow {
  transform: rotate(90deg);
}

.help-section-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease-out;
}

.help-section.expanded .help-section-content {
  max-height: 500px;
}

.help-section-text {
  padding: 16px 20px;
  border-top: 1px solid #e5e7eb;
  background-color: #f9fafb;
  line-height: 1.6;
  color: #4b5563;
  font-size: 16px;
}

.help-footer {
  margin-top: 32px;
  padding: 24px;
  background-color: #f0f7ff;
  border-radius: 8px;
}

.help-footer-content {
  display: flex;
  gap: 16px;
}

.help-footer-text {
  flex: 1;
}

.help-footer-text h3 {
  font-size: 18px;
  color: #1a1a1a;
  margin-bottom: 8px;
}

.help-footer-text p {
  color: #4b5563;
  margin-bottom: 16px;
}

.help-footer-buttons {
  display: flex;
  gap: 12px;
}

.btn {
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  border: none;
  transition: background-color 0.2s;
}

.btn.primary {
  background-color: #2563eb;
  color: white;
}

.btn.primary:hover {
  background-color: #1d4ed8;
}

.btn.secondary {
  background-color: white;
  color: #2563eb;
  border: 1px solid #bfdbfe;
}

.btn.secondary:hover {
  border-color: #93c5fd;
}