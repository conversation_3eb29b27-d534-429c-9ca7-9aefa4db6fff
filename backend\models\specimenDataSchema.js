const mongoose = require('mongoose');

const specimenDataSchema = new mongoose.Schema({
  LAB: { type: String, required: true },
  PATIENT_ID: { type: String, required: true },
  FULL_NAME: { type: String, required: true },
  SEX: { type: String, enum: ['1', '0'], required: true }, // '1' for male, '0' for female
  AGE: { type: String, required: true },
  PHONE: { type: String },
  ADDRESS: { type: String },
  ID_NO: { type: String },

  // Hospital Information
  HOSPITAL_NO: { type: String },
  WARD: { type: String },
  WARD_TYPE: { type: String },
  INSTITUT: { type: String },
  DEPARTMENT: { type: String },
  SPEC_NUM: { type: String, required: true, unique: true },  // Unique identifier for the specimen
  SPEC_DATE: { type: Date },
  SPEC_TYPE: { type: String },

  // Pathology Information
  SMEAR_RESULT: { type: String },
  CULTURE_RESULT: { type: String },
  ESBL: { type: String, enum: ['yes', 'no'] },
  BETA_LACT: { type: String, enum: ['yes', 'no'] },
  INDUC_CLI: { type: String, enum: ['yes', 'no'] },
  CARBAPENEM: { type: String, enum: ['yes', 'no'] },
  COMMENT: { type: String },
  
  // Test Information
  testDataList: [
    {
      
      testMethod: { type: String },
      testResult: { type: String },
    
    },
  ],
  create_time: { type: Date, default: Date.now },  // 创建时间
  update_time: { type: Date, default: Date.now }  // 更新时间
}, { collection: 'SpecimenData' });

// 在保存或更新时自动更新更新时间
specimenDataSchema.pre('save', function (next) {
    this.update_time = Date.now();
    next();
  });
module.exports = mongoose.model('SpecimenData', specimenDataSchema);

