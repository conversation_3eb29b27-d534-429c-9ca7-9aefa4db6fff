import React, { useState,useEffect,useImperativeHandle, useRef  } from 'react';
import './models/MICData';
import './QueryForm.css';
import './CompoundTable.css';
import './BacterialSensitivity.css';
import MICData from './models/MICData';
import CustomDialog from './componets/CustomDialog';
import GenerateReportPage from "./componets/ui/GenerateReportPage";
import PhenotypeChartModal from './componets/ui/PhenotypeChartModal';

import { useSharedState } from './SharedStateContext';
import { useToast } from './componets/ToastProvider';
import { Modal } from "antd";
import { Lightbulb,BellRing,Upload,ShieldQuestion,Download} from 'lucide-react';
import { antibioticsDataStore } from './models/AntibioticsDataStore';
import { resultsDataStore } from './models/ResultsDataStore';
import { Select, Checkbox, Button } from "antd";
import * as XLSX from "xlsx";


const MICForm =({ handleNavigation })=> {
  const [bacteriaName, setBacteriaName] = useState('');
  const [bacteriaId, setBacteriaId] = useState('');
  const [antibioticCode, setAntibioticCode] = useState('');
  const [micValue, setMicValue] = useState('');
  const [result, setResult] = useState(null);
  // const [results, setResults] = useState([]);
  const [results, setResults] = useState(resultsDataStore.getResults());
  const [antibiotics, setAntibiotics] = useState([]);
  const [requestDataList, setRequestDataList] = useState([]);
  const [specimenNumbers, setSpecimenNumbers] = useState([]);
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [isComposing, setIsComposing] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [suggestions, setSuggestions] = useState([]);
  const [selectedLevel, setSelectedLevel] = useState(null);
  const [selectedParentId, setSelectedParentId] = useState(null);
  // const [antimicrobialAgents, setAntimicrobialAgents] = useState([]);
  const [antimicrobialAgents, setAntimicrobialAgents] = useState(antibioticsDataStore.getData() || []);
  const [selectedAgent, setSelectedAgent] =useState('');
  const { state, dispatch } = useSharedState();
  const [dialogVisible, setDialogVisible] = useState(false);
   const [hintMessage,setHintMessage] = useState('');
   const [selectedMethod,setSelectedMethod] = useState('');
   const [diskContent,setDiskContent] = useState('');
   const [isModalVisible, setIsModalVisible] = useState(false);
   const [isReportModalVisible, setIsReportModalVisible] = useState(false);
   const [selectedAuthorities, setSelectedAuthorities] = useState([]);
   const [selectedBacteriaCode, setSelectedBacteriaCode] = useState([]);
   const [file, setFile] = useState(null);
   const [isPhenotypeModalOpen, setIsPhenotypeModalOpen] = useState(false);
   
   const authorities = ["CLSI", "EUCAST", "FDA", "SELF"];
 
   const resultContainerRef = useRef(null);
   const fileInputRef = useRef(null);
  const { showToast } = useToast();
  const COMMON_BACTERIA = [
    { bacteriaCode: "nme", bacteriaName: "脑膜炎奈瑟菌" },
    { bacteriaCode: "efa", bacteriaName: "粪肠球菌" },
    { bacteriaCode: "bca", bacteriaName: "卡他莫拉菌" },
    { bacteriaCode: "aba", bacteriaName: "鲍曼不动杆菌" },
    { bacteriaCode: "spn", bacteriaName: "肺炎链球菌" },
    { bacteriaCode: "sep", bacteriaName: "表皮葡萄球菌" },
    { bacteriaCode: "pae", bacteriaName: "铜绿假单胞菌" },
    { bacteriaCode: "kpn", bacteriaName: "肺炎克雷伯菌" },
    { bacteriaCode: "sma", bacteriaName: "粘质沙雷菌" },
    { bacteriaCode: "sau", bacteriaName: "金黄色葡萄球菌" },
    { bacteriaCode: "pmi", bacteriaName: "奇异变形杆菌" },
    { bacteriaCode: "mmo", bacteriaName: "摩根摩根菌" }
  ];
    
  const BASE_URL = process.env.REACT_APP_BASE_URL;

  useEffect(() => {
    if (resultContainerRef.current) {
     
      resultContainerRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'end',
      });
    }
  }, [results]); 



  const handleShowModal = () => {
    setIsModalVisible(true);
  };

  const handleCloseModal = () => {
    setIsModalVisible(false);
  };


  
  useEffect(() => {
    if (inputValue.length > 1) {
      setError('');
      setSuggestions([]);
      fetchSuggestion();
     
    }
    
  }, [inputValue]); // 依赖与输入相关
  
  useEffect(() => {
    if (bacteriaId) {
    
        setAntimicrobialAgents([]);
        setError('');
        fetchAgents();
    }
  }, [bacteriaId]);


useEffect(() => {
  return () => {
    
    antibioticsDataStore.setData(antimicrobialAgents);
  };
}, [antimicrobialAgents]);


  useEffect(() => {
    if (state.patient) {
     
      // Get TestData array and extract distinct specimenNumber
      const specs = [...new Set(state.patient.getTestData().map((data) => data.specimenNumber))];
      setSpecimenNumbers(specs);
      setError('');
      setSuggestions([]);
      setInputValue(state.patient.cultureResult)
      // fetchSuggestion();
     
    }
  }, [state.patient]);
  
  useEffect(() => {
    // 每次 results 更新时，存入单例
    resultsDataStore.setResults(results);
  }, [results]);
  

  const fetchSuggestion= async() =>{
    try {
       if(!inputValue){
         return;
       } 
      // setResults([]);
        const response = await fetch(`${BASE_URL}api/suggestions?query=${inputValue}`);
        if (!response.ok) {
          throw new Error('Network re sponse was not ok');
        }
        const data = await response.json();
        
          
        if(data && data.length>0){
          setSuggestions(data);
          setSelectedParentId(data[0].parent_id);
          setSelectedLevel(data[0].level);
          setBacteriaName(data[0].name);
          setBacteriaId(data[0].bacteriaId);
          sessionStorage.removeItem("requestdata"); 
          setInputValue('');
          setError('');
        }
   } catch (error) {
      console.error('Error fetching suggestions:', error);
    }
    
  };
  
  

  const fetchAgents = async () => {
    try {
      if (antibioticsDataStore.getData().length > 0) {
        setAntimicrobialAgents(antibioticsDataStore.getData());
      
        return;  // 直接从单例类加载数据，避免重复请求
      }
     
      const response = await fetch(`${BASE_URL}api/getAgents?bacteriaId=${bacteriaId}`);
      if (!response.ok) {
        throw new Error('Network response was not ok');
      }
      const data = await response.json();
  
      if (data.agents.length > 0) {
        const transformedAgents = data.agents.map(agent => ({ name: agent, diskContent: "", micValue: "" }));
        setAntimicrobialAgents(transformedAgents);
        antibioticsDataStore.setData(transformedAgents);  // 存入单例类
      } else {
        setError('系统没有发现可用的MIC,请转到医药词典->新建Mic值');
      }
      setSelectedLevel('');
      setSelectedParentId('');
      setInputValue('');
  
    } catch (error) {
      setError('系统没有发现可用的MIC,请转到医药词典->新建Mic值');
    }
  };

  const handleFileChange = (e) => {
    setFile(e.target.files[0]);
  };

  const handleQuery = async (code) => {
    try {
    
      const response = await fetch(`${BASE_URL}antibiotics/${code.toUpperCase()}`);
      
      if (response.ok) {
        const data = await response.json();
        const agentName = data?.['DISPLAY_NAME'] || "";
        return agentName;     
      } else {
        console.log('未找到相关数据');
      }

    } catch (error) {
      console.log('查询失败：' + (error.response?.data?.message || '未找到该抗生素'));
    } finally {
      
    }
  };

  const handleBactery = async (bacterName) => {
    try {
      let response = await fetch(`${BASE_URL}api/suggestions?query=${bacterName}`);
      if (!response.ok) {
        throw new Error('Network re sponse was not ok');
      }
      const data = await response.json();
      if(data && data.length>0){
         return data;
        }
      return null; 
      
      } catch (error) {
          console.error('Error fetching suggestions:', error);
        }
  };

  const handleExport = () => {
    
    const templateUrl = `${process.env.PUBLIC_URL + "mic_template.xltx"}`;

    // 创建一个临时 <a> 标签
    const link = document.createElement("a");
    link.href = templateUrl; // 指向模板路径
    link.download = "mic_template.xltx" 
    document.body.appendChild(link);

    // 触发下载
    link.click();

    // 清理临时元素
    document.body.removeChild(link);
  };

  const handleFileUpload = async (event) => {
    const file = event.target.files[0];
    if (!file) return;
  
    const reader = new FileReader();
    reader.onload = async (e) => {
      const workbook = XLSX.read(e.target.result, { type: "binary" });
      const sheetName = workbook.SheetNames[0];
      const sheet = workbook.Sheets[sheetName];
      const jsonData = XLSX.utils.sheet_to_json(sheet, { header: 1 });
  
      if (jsonData.length < 2) return;
  
      const headers = jsonData[0]; // 获取表头
      const dataRows = jsonData.slice(1); // 获取数据部分
  
      const bacteriaIndex = headers.indexOf("ORGANISM"); // 细菌名称列
      const antibioticStartIndex = headers.indexOf("AMK_NM"); // 找到抗生素起始列
  
      if (bacteriaIndex === -1 || antibioticStartIndex === -1) {
        console.error("未找到 'ORGANISM' 或 'AMK_NM' 列");
        return;
      }
  
      const antibioticColumns = headers.slice(antibioticStartIndex); // 只保留抗生素列
      const formattedData = [];
      
     
      for (const row of dataRows) {
        const data=await handleBactery(row[bacteriaIndex])
        const bacteriaName =data[0].name;
        const bacteriaId = data[0].bacteriaId; 
        const selectedMethod="MIC";
        const diskContent="";
        if (!bacteriaId) continue; // 跳过无法匹配的细菌
  
        // **3. 并行查询所有抗生素名称**
        const antibioticPromises = antibioticColumns.map(async (antibiotic, index) => {
          const micValue = row[antibioticStartIndex + index];
         
          if (micValue !== undefined && micValue !== "") {
            const selectedAgent = await handleQuery(antibiotic.replace("_NM", "").trim());
            const antibioticObj = antimicrobialAgents.find(a => a.name === selectedAgent);
            if (!antibioticObj) return null; // 如果 selectedAgent 不在 antibiotics 列表中，则跳过
            antibioticObj.micValue = micValue;
          
            return {
              bacteriaId,
              bacteriaName,
              selectedAgent,
              micValue,
              diskContent,
              selectedMethod,
            };
          }
          return null;
        });
  
        const results = await Promise.all(antibioticPromises);
    
        formattedData.push(...results.filter(item => item)); // 过滤掉 null
      }
  
      //  console.log(formattedData);
      setRequestDataList(formattedData);
      setError('');
    //  console.log(formattedData)
      doSearch(formattedData);
    };
  
    reader.readAsBinaryString(file);
  };

  if(!state.patient)
  return (
    <div className="report-container">
      <div className="patient-selection-content">
        <ShieldQuestion size={35} />
        <span className="patient-selection-text">还没有选择标本信息!!</span>
      </div>
    </div>
  );

  const getUniqueSpecimenNumbers = () => {
    const testData = state.patient.getTestData(); // Assuming this returns an array of TestData objects
    if (!testData || testData.length === 0) {
      return [];
    }
    const uniqueSpecimenNumbers = Array.from(
      new Set(testData.map((item) => item.specimenNumber))
    );
    return uniqueSpecimenNumbers;
  };

 


  const handleGenerateReport = () => {
     
    if (selectedAuthorities.length==0) {
      setHintMessage("选择权威机构");
      setDialogVisible(true);
      return;
    }
    if (!results || results.length === 0) {
      setHintMessage("没有可生成的报告数据");
      setDialogVisible(true);
      return;
    }
   
    let Mic = [];
    results.forEach((result, index) => {
      let ecv="";
      // console.log(result)
      result.breaking.map((selectedbreaking)=>{
           
              if(result.selectedMethod==="MIC"){
                const sValue = selectedbreaking?.["S (μg/mL)"] ?? "";
                const rValue = selectedbreaking?.["R (μg/mL)"] ?? "";
                const iValue = selectedbreaking?.["I (μg/mL)"] ?? "";
                const rOrIValue = rValue || iValue;
                ecv=`${sValue}${rOrIValue ? `,${rOrIValue}` : ""}`;
              }
              if (result.selectedMethod === "Disk") {
                const s_mmValue = selectedbreaking?.["S (mm)"] ?? "";
                const r_mmValue = selectedbreaking?.["R (mm)"] ?? "";
                const i_mmValue = selectedbreaking?.["I (mm)"] ?? "";
                const rOrI_mmValue = r_mmValue || i_mmValue;
                ecv = `${s_mmValue}${rOrI_mmValue ? `,${rOrI_mmValue}` : ""}`;
              }
              if (result.selectedMethod === "MIC+Disk") {
                const sValue = selectedbreaking?.["S (μg/mL)"] ?? "";
                const rValue = selectedbreaking?.["R (μg/mL)"] ?? "";
                const s_mmValue = selectedbreaking?.["S (mm)"] ?? "";
                const r_mmValue = selectedbreaking?.["R (mm)"] ?? "";
                const micPart = `${sValue}${rValue ? `,${rValue}` : ""}`;
                const diskPart = `${s_mmValue}${r_mmValue ? `,${r_mmValue}` : ""}`;
                ecv = `${micPart}${micPart && diskPart ? ";" : ""}${diskPart}`;
              }
            
              const micData = new MICData();
              micData.Agent = result.selectedAgent ?? "未知药物";
              micData.ECV = ecv;
              micData.Bacteria = result.bacteriaName ?? "未知细菌";
              micData.BacteriaCode = selectedBacteriaCode ?? "未知细菌";
              micData.BacteriaId = bacteriaId ?? "未知细菌";
              micData.MIC = selectedbreaking?.micValue ?? "未知MIC值";
              micData.DISK = selectedbreaking.diskContent ?? "未知disk值";
              micData.SORT = selectedbreaking?.sensitivity ?? "未知敏感性分类";
              micData.Comments=selectedbreaking.comments;
              micData.Remark =result.remark;
              micData.drugDeduction=result.drugInterpretation;
              micData.esblAlert=result.ESBL_Alert;
              micData.specimenNumber = state.patient.specimenNumber || "未指定标本编号";
              micData.selectedMethod=result.selectedMethod;
              micData.selectedAuthority=selectedAuthorities;
              micData.expertics=selectedbreaking.expertics;
              Mic.push(micData);
      });

    });
   
      // console.log(Mic)
   
  
    if (Mic.length === 0) {
      setHintMessage("报告生成失败，数据不足");
      setDialogVisible(true);
      return;
    }
   
  //   const filteredBreaking = Mic.filter((record) =>
  //   selectedAuthorities.some((authority) =>
  //     record.Comments && record.Comments.includes(authority) 
  //   )
  // );
   
    // console.log(filteredBreaking);
    
    // Dispatch the generated report
    dispatch({ type: "SET_MIC", payload: Mic });
    handleNavigation('report');
  };
  

  const handleAddRow = () => {
    setAntimicrobialAgents((prev) => [
      ...prev,
      { name: '', diskContent: '', mic: '' }, // 确保新行有默认值
    ]);
  };
  
  const handleInputChange = (index, value) => {
    setAntimicrobialAgents((prev) =>
      prev.map((agent, i) =>
        i === index ? { ...agent, name: value } : agent
      )
    );
  };
  
  const handleDiskContentChange = (index, value) => {
    const updatedAgents = [...antimicrobialAgents];
    updatedAgents[index] = { ...updatedAgents[index], diskContent: value };
    setAntimicrobialAgents(updatedAgents);
  };
  
  const handleMicValueChange = (index, value) => {
    const updatedAgents = [...antimicrobialAgents];
    updatedAgents[index] = { ...updatedAgents[index], micValue: value };
    setAntimicrobialAgents(updatedAgents);
  };
  
 
  const handleCompositionStart = () => {
    setIsComposing(true);
  };

  const handleCompositionEnd = (e) => {
   
    setIsComposing(false);
    
  };
   
  const handleClose = () => {
    setDialogVisible(false);
  };


  const deleteMicInfo= async()=>{
    setResults([]);
    
  };
  const resetForm = () => {
    setBacteriaName('');
    setSelectedAgent('');
    setSelectedLevel(null);
    setSelectedParentId(null);
    setSuggestions([]);
    setMicValue('');
    setAntimicrobialAgents([]);
    setInputValue('');
    setSelectedMethod('');
    setDiskContent('');
    setBacteriaId('');
   // setResults([]);
   
    setError('');
   };

    const renderComments=(comments)=> {
      if (!comments) return null;
    
      // Replace "参考表3A)" with a clickable span
      const parts = comments.split("参考表3A)");
      
      return (
        <>
          {parts.map((part, index) => (
            <React.Fragment key={index}>
              {part}
              {index < parts.length - 1 && (
                <span
                style={{ cursor: "pointer" }}
                onClick={handleShowModal}
              >
                  参考表3A)
                </span>
              )}
            </React.Fragment>
          ))}
        </>
      );
    }


  const handleSubmit = async (e) => {
    try {
      e.preventDefault();
      const updatedAntibiotics = [...antimicrobialAgents];
      const filteredAntibiotics = updatedAntibiotics.filter(antibiotic => antibiotic.diskContent || antibiotic.micValue);
      let data=[];
      for (let i = 0; i < filteredAntibiotics.length; i++) {
          const antibiotic = filteredAntibiotics[i];
          let selectedAgent=antibiotic.name;
          let selectedMethod="MIC";
          let micValue="";
          let diskContent="";
          // 判断选择的测试方法（Disk + MIC，Disk，MIC）
          if (antibiotic.micValue && antibiotic.diskContent) {
            selectedMethod ="DISK+MIC";
            micValue=antibiotic.micValue;
            diskContent=antibiotic.diskContent;
          } else if (antibiotic.diskContent) {
              selectedMethod="Disk";
            diskContent=antibiotic.diskContent;
          } else if (antibiotic.micValue) {
            selectedMethod="MIC";
            micValue=antibiotic.micValue;
          }
      
          // 创建请求数据
          const request_data = { bacteriaId,bacteriaName,selectedAgent, micValue,diskContent,selectedMethod };
          data.push(request_data);  
          // 等待请求完成
      }
     
      if(data.length>0)
         await doSearch(data);

      
      // const clearedAntibiotics = antimicrobialAgents.map((antibiotic) => {
      //   // If micValue or diskContent exists, reset them to empty strings
      //   if (antibiotic.micValue || antibiotic.diskContent) {
      //     return {
      //       ...antibiotic,
      //       diskContent: '',
      //       micValue: ''
      //     };
      //   }
      //   return antibiotic;  // Keep the rest of the antibiotic data intact
      // });
      
      // // Update state with cleared antibiotics
      // setAntimicrobialAgents(clearedAntibiotics);
    } catch (error) {
      
    }

  };

  const doSearch= async (data)=>{
    try {
      
      const response = await fetch(`${BASE_URL}api/multimethods/breaking`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });
  
      if (response.ok) {
        const resultData = await response.json();
       
        if(resultData && resultData.length > 0){
         
          // setResults(prevResults => {
          //   const updatedResults = [...prevResults, ...resultData];
  
          //   // 存入单例
          //   resultsDataStore.setResults(updatedResults);
          //   return updatedResults;
          // });
         
          setResults(resultData);
          // console.log(resultData);                
          if(resultData[0].remark)
            showToast("注意查看警告信息");
          if(resultData[0].ESBL_Alert)
            showToast("警报:ESBL表型检测阳性(符合CLSI ESBL筛查标准)"); 
        }
        else{
          setError('系统没有发现可用的MIC,请转到医药词典->新建Mic值');
          return;
        }
      
      
      } else {
        setError('查询失败:No matching breaking data found');
        return;
       
      }
    } catch (error) {
      setError('系统没有发现可用的MIC,请转到医药词典->新建Mic值');
    }finally {
      setLoading(false);
     
    }
  };

  // 定义 openPhontypechart 和 closePhontypechart，确保作用域可用
const openPhontypechart = () => setIsPhenotypeModalOpen(true);
const closePhontypechart = () => setIsPhenotypeModalOpen(false);

  return (
        <div className="container">
        <h6 style={{ textAlign: 'center', margin: 10, color: 'white', fontSize: '16px' }}> 用药指导<br /> {state.patient ? '当前执行患者：' + state.patient.name : '还没有选择患者'}</h6>
        <div className="icons">
     <input
          type="file"
          accept=".xlsx, .xls"
          onChange={handleFileUpload }
          style={{ display: "none" }}  // 隐藏原生的上传按钮
          id="file-input"
        />
    <button
          type="button"
          onClick={() => document.getElementById("file-input").click()}
          title="导入数据"
          style={{ background: "none", border: "none", cursor: "pointer" }}
        >
          <Upload size={20} />
        </button>

    {/* 下载按钮 */}
    <button  type="button" onClick={handleExport} title="下载模板" 
      style={{ background: "none", border: "none", cursor: "pointer",padding:"10px" }}
    >
      <Download size={20} />
    </button>
  </div>

       
        <form className="query-form" onSubmit={handleSubmit}>
        {/* <div className="form-group">
             <label className="form-label">&nbsp;</label>
             <div style={{ position: 'relative', width: '100%' }}>
             <select 
                className='agent-select'
                value={inputValue ? inputValue.bacteriaCode : ""}  // Store bacteriaCode as the value
                onChange={(e) => {
                  const selectedOption = e.target.selectedOptions[0];
                  const selectedBacteriaCode = selectedOption.value;
                  const selectedBacteriaName = selectedOption.textContent;
                  setSelectedBacteriaCode(selectedBacteriaCode);
                  setInputValue(selectedBacteriaName);
                }}
              >
                <option value="">请选择常用菌株</option>
                {COMMON_BACTERIA.map((bacteria, index) => (
                  <option key={index} value={bacteria.bacteriaCode}>
                    {bacteria.bacteriaName}
                  </option>
                ))}
              </select>


            </div>
            
          </div> */}



       
        <div className="form-group">

      {/* {suggestions.length > 0 ? (
        <>
          <label className="form-label">细菌：</label>
          <div style={{ position: 'relative', width: '100%' }}>
          <select className="agent-select" value={bacteriaId}  onChange={(e) => {setBacteriaId(e.target.value) } }>

            {suggestions.map((suggestion, index) => (
              <option key={index} value={suggestion.bacteriaId}>
                {suggestion.name}
              </option>
            ))}
          </select>
          </div>
        </>
      ) : (
        <> */}


        


          <label className="form-label">细菌：</label>
          <div style={{ position: 'relative', width: '100%' }}>
            <input
              type="text"
              value={bacteriaName}
              onChange={handleInputChange}
              onCompositionStart={handleCompositionStart}
              onCompositionEnd={handleCompositionEnd}
              className="mic-input" />
          </div>

         
       
        {/* </> */}
      {/* )} */}


      </div>
      {/* change here */}
      <div className="form-group">
  {antimicrobialAgents.length > 0 ? (
    <>
    
       <table className="bacter_table">
        <thead>
          <tr>
            <th className="bacter_header_main">抗菌药物</th>
             <th className="bacter_header_main">Disk<br/>(μg)</th>
            <th className="bacter_header_main">MIC<br/>(mg/L)</th>
            <th className="bacter_header_main">抗菌药物</th>
            <th className="bacter_header_main">Disk<br/>(μg)</th>
            <th className="bacter_header_main">MIC<br/>(mg/L)</th>
          </tr>
        </thead>
        <tbody>
  {(() => {
    const rows = [];
    for (let i = 0; i < antimicrobialAgents.length; i += 2) {
      rows.push(
        <tr key={i}>
          <td>
            <input
              type="text"
              value={antimicrobialAgents[i].name || ''}
              onChange={(e) => handleInputChange(i, e.target.value)}
              className="bacteria_mic-input"
            />
          </td>
          <td>
            <input
              type="text"
              value={antimicrobialAgents[i]?.diskContent || ''}
              onChange={(e) => handleDiskContentChange(i, e.target.value)}
              className="bacteria_mic-input"
            />
          </td>
          <td>
            <input
              type="text"
              value={antimicrobialAgents[i]?.micValue || ''}
              onChange={(e) => handleMicValueChange(i, e.target.value)}
              className="bacteria_mic-input"
            />
          </td>
          {i + 1 < antimicrobialAgents.length && (
            <>
              <td>
                <input
                  type="text"
                  value={antimicrobialAgents[i + 1].name || ''}
                  onChange={(e) => handleInputChange(i + 1, e.target.value)}
                  className="bacteria_mic-input"
                />
              </td>
              <td>
                <input
                  type="text"
                  value={antimicrobialAgents[i + 1]?.diskContent || ''}
                  onChange={(e) => handleDiskContentChange(i + 1, e.target.value)}
                  className="bacteria_mic-input"
                />
              </td>
              <td>
                <input
                  type="text"
                  value={antimicrobialAgents[i + 1]?.micValue || ''}
                  onChange={(e) => handleMicValueChange(i + 1, e.target.value)}
                  className="bacteria_mic-input"
                />
              </td>
            </>
          )}
        </tr>
      );
    }
    return rows;
  })()}
</tbody>

      </table>
    </>
  ) : (
    ""
  )}
</div>
      {/* chenge end here */}

          {/* Error and submit/reset buttons */}
          {error && (
            <div style={{ color: 'red', marginBottom: '10px', textAlign: 'center', fontSize: '16px' }}>
              {error}
            </div>
          )}

          {antimicrobialAgents.length>0 && ( <div className="submit-button-container">
            <button type="submit" className="submit-button">查询</button>
            <button type="button" className="reset-button" onClick={resetForm}>重置</button>
            <button type="button" className="reset-button" onClick={handleAddRow}>添加抗菌药物</button>
        
          </div>)}
         

          <div>

            {dialogVisible && (
              <CustomDialog
                message={hintMessage}
                onClose={handleClose} />
            )}
          </div>

         
        </form>
    
        
        <div>
    
        <Modal
        title="参考表3A"
        visible={isModalVisible}
        footer={null}
        onCancel={handleCloseModal}
        centered
        width={902} // Width matches image resolution
        bodyStyle={{
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          padding: "10px",
        }}
      >
        <img
          src={`${process.env.PUBLIC_URL}/table3a.png`}
          alt="Table 3A"
          style={{ width: "902px", height: "519px", marginBottom: "10px" }}
        />
        <img
          src={`${process.env.PUBLIC_URL}/table3a-1.png`}
          alt="Table 3A-1"
          style={{ width: "902px", height: "519px" }}
        />
      </Modal>
    </div>
    <div ref={resultContainerRef} >
        {results && results.length > 0 && (
       
          <table className="compound-table">
            <thead>
              <tr>
                <th rowSpan="2" className="first-col">抗菌药物</th>
                <th rowSpan="2">纸片含量<br />(μg)</th>
                <th colSpan="3" className="group-header">纸片扩散法<br />(mm)</th>
                <th colSpan="3" className="group-header">MIC<br />(μg/mL)</th>
                <th rowSpan="2" >解析</th>
              </tr>
             <tr className="sub-header">
                <th>S</th>
                <th>R</th>
                <th>DISK(值)</th>
                <th>S</th>
                <th>R</th>
                <th>MIC(值)</th>
              </tr>
            </thead>
            <tbody>
            {results.slice().reverse().map((result, index) => (
  <React.Fragment key={index}>
    {/* Inner Loop: Render breaking records */}
    {result.breaking.map((breakRecord, breakIndex) => (
      <React.Fragment key={`${index}-${breakIndex}`}>
        {/* Main Data Row */}
        <tr>
          <td>{result.selectedAgent}</td>
          <td>{breakRecord['Disk Content'] ? breakRecord['Disk Content'] : "-"}</td>
          <td>{breakRecord['S (mm)'] ? breakRecord['S (mm)'] : "-"}</td>
          <td>{breakRecord['R (mm)'] ? breakRecord['R (mm)'] : "-"}</td>
          <td>{result.diskContent ? result.diskContent : "-"}</td>
          <td>{breakRecord['S (μg/mL)'] ? breakRecord['S (μg/mL)'] : "-"}</td>
          <td>{breakRecord['R (μg/mL)'] ? breakRecord['R (μg/mL)'] : "-"}</td>
          <td>{result.micValue?result.micValue:"-"}</td>
          <td>{breakRecord.sensitivity?breakRecord.sensitivity:"-"}</td>
        </tr>

        {/* Comments Row */}
        {breakRecord.comments && (
          <tr>
            <td colSpan="10" className="text-left">
              <Lightbulb style={{ marginRight: '0.2rem' }} size={20} />
              <span>{breakRecord.comments}</span>
            </td>
          </tr>
        )}
      </React.Fragment>
    ))}

    {/* Optional Remark Section */}
    {result.remark && (
      <tr>
        <td colSpan="10" className="text-left">
          <BellRing style={{ marginRight: '0.2rem', color: 'red' }} size={20} />
          <span>{result.remark}</span>
        </td>
      </tr>
    )}

{result.drugInterpretation && (
      <tr>
        <td colSpan="10" className="text-left">
          <BellRing style={{ marginRight: '0.2rem', color: 'red' }} size={20}  />
          <span>{result.drugInterpretation}</span>
        </td>
      </tr>
    )}
{result.ESBL_Alert && (
      <tr>
        <td colSpan="10" className="text-left">
          <BellRing style={{ marginRight: '0.2rem', color: 'red' }} size={20}  />
          <span>{result.ESBL_Alert}</span>
        </td>
      </tr>
    )}
  </React.Fragment>
))}



            </tbody>
          </table>
       
        
        )}
        </div>
        {results && results.length > 0 && (
  <div className="submit-button-container">
    {/* Include the GenerateReportPage Component */}
    <div style={{ display: "flex", alignItems: "center", gap: "20px", width:"100%" }}>
        <Checkbox.Group
          style={{ display: "flex", flexDirection: "row", gap: "10px" }}
          options={authorities.map(auth => ({
            label: <span style={{ color: "white" }}>{auth}</span>,
            value: auth
          }))}
          onChange={setSelectedAuthorities}
        />
        <Button type="primary" onClick={handleGenerateReport}>
          生成报告
        </Button>
      </div>

    {/* Generate Report Button */}
   
  </div>
)}
 {results && results.length > 0 && (
<div className="submit-button-container">
  {/* Clear Screen Button */}
    <button
      type="button"
      onClick={deleteMicInfo}
      className="delete-button"
    >
      清空屏幕
    </button>
</div>
 )}



      </div>
  );
   };



export default MICForm;