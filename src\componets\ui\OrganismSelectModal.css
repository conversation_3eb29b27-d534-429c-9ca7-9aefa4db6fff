.organism-modal-overlay {
  position: fixed;
  top: 0; left: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.2);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}
.organism-modal {
  background: #fff;
  border-radius: 8px;
  width: 400px;
  max-height: 70vh;
  overflow: auto;
  box-shadow: 0 2px 16px rgba(0,0,0,0.2);
  padding: 16px;
}
.organism-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
  margin-bottom: 8px;
}
.organism-modal-search {
  width: 100%;
  margin-bottom: 8px;
  padding: 4px 8px;
  font-size: 14px;
}
.organism-modal-list {
  max-height: 50vh;
  overflow-y: auto;
}
.organism-modal-item {
  padding: 6px 0;
  border-bottom: 1px solid #eee;
  cursor: pointer;
}
.organism-modal-item:hover {
  background: #f0f0f0;
}
