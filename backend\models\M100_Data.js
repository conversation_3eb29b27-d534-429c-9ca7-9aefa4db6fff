const mongoose = require('mongoose');

const m100breakingSchema = new mongoose.Schema({
  table_id: { type: String }, // 表格编号
  organism_group: { type: String }, // 菌群
  drug_class: { type: String }, // 药物类别
  antimicrobial_agent: { type: String, required: true }, // 药物名称
  mic_s_ug_ml: { type: String },
  mic_sdd_ug_ml: { type: String },
  mic_i_ug_ml: { type: String },
  mic_r_ug_ml: { type: String },
  notes: { type: String },
  create_date: { type: Date, default: Date.now },
  update_date: { type: Date, default: Date.now }
}, { collection: 'm100_data' });

m100breakingSchema.pre('save', function (next) {
  this.update_date = Date.now();
  next();
});

module.exports = mongoose.model('M100Breaking', m100breakingSchema);