const mongoose = require('mongoose');

const whonetRuleSchema = new mongoose.Schema({
  RuleSet: { type: String },  
  EXPERTTYPE: { type: String },  
  CATEGORY: { type: String },  
  ACTIVE: { type: String },  
  PRIORITY: { type: String },  
  ORG_GROUP: {type: String },  
  ORG_CODES: { type: String },  
  DESCRIPTION: { type: String },  
  ABX_RESULT: { type: String },  
  OTH_RESULT: { type: String },  
  INTERP: { type: String },  
  MICROBIOL: {type: String },  
  CLINICAL: { type: String },  
  QUALITY: { type: String },  
  QUAL_TYPE: { type: String },  
  IMP_SPECIE: { type: String },  
  IMP_RESIST: { type: String },  
  SAVE_ISOL: {type: String },  
  SEND_REF: { type: String },  
  INF_CONT: { type: String },  
  RX_COMMENT: { type: String },  
  CREATE_TIME: { type: Date, default: Date.now },  // 创建时间，默认当前时间
  UPDATE_TIME: { type: Date, default: Date.now }  // 更新时间，默认当前时间
}, { collection: 't_dict_expertise_cn' });

// 保存前更新更新时间
whonetRuleSchema.pre('save', function (next) {
  this.UPDATE_TIME = Date.now();
  next();
});

module.exports = mongoose.model('WhonetRules', whonetRuleSchema);
