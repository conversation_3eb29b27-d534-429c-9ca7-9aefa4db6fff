const mongoose = require('mongoose');

const userMicSchema = new mongoose.Schema({
  newId: { type: String, required: true },
  bacteriaType: { type: String, default: '' },
  antimicrobialAgent: { type: String, required: true },
  diskContent: { type: String, default: '' },
  sBreakpoint: { type: String, default: '' },
  rBreakpoint: { type: String, default: '' },
  micBreakpoint: { type: String, default: '' },
  notes: { type: String, default: '' },
  sensitivityMethod: { type: String, default: '' },
  antibioticCode: { type: String, required: true }
}, { collection: 'UserMic' });

module.exports = mongoose.model('UseMicModel', userMicSchema);
