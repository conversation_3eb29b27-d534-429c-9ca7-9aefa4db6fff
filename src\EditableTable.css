/* EditableTable.css */
.mscontainer {
  width: 100%;
  max-width: 1580px; /* 设置最大宽度 */
  margin: 0 auto;
  padding: 20px;
  box-sizing: border-box; /* 确保内边距不影响整体宽度 */
}

  
  /* 表格中的按钮 */
  button {
    padding: 8px 12px;
    margin: 2px;
    border: none;
    border-radius: 4px;
    color: #fff;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
  }
  
  /* 添加按钮 */
  button.add-btn {
    background-color: #28a745; /* 绿色 */
  }
  
  button.add-btn:hover {
    background-color: #218838;
  }
  
  /* 删除按钮 */
  button.delete-btn {
    background-color: #dc3545; /* 红色 */
  }
  
  button.delete-btn:hover {
    background-color: #c82333;
  }
  
  /* 保存按钮 */
  button.save-btn {
    background-color: #007bff; /* 蓝色 */
  }
  
  button.save-btn:hover {
    background-color: #0056b3;
  }
  
  /* 底部操作栏样式 */
  .table-footer {
    margin-top: 20px;
    display: flex;
    gap: 10px;
    justify-content: flex-start;
  }
  
  .table-footer button {
    padding: 10px 20px;
    font-size: 14px;
    font-weight: bold;
  }

    /* 导入按钮样式 */
  .import-btn {
    display: inline-block;
    background-color: #17a2b8; /* 蓝绿色 */
    color: white;
    padding: 6px 12px;
    font-size: 12px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease;
  }

  .import-btn:hover {
    background-color: #138496;
  }

  .title-container {
    display: flex;
    justify-content: space-between;
    align-ite: center;
    margin-bottom: 10px;
  }
  
  /* 图标容器 */
  .icons {
    display: flex;
    align-ite: center;
  }
  
  .icons button {
    padding: 0;
    margin: 0;
    outline: none;
  }

  .settings-container {
    display: flex;
    flex-direction: column;
    gap: 5px; /* Small spacing between rows */
    margin-bottom: 20px; /* Space between settings and the table */
  }
  
  .inputs-row {
    display: flex;
    align-ite: center; /* Align ite vertically */
    gap: 10px; /* Space between ite */
  }
  
  .inputs-row label {
    font-size: 14px;
    margin-right: 5px;
    color: black;
  }
  
  .inputs-row select,
  .inputs-row input {
    padding: 5px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 14px;
    min-width: 200px;
  }
  
  .inputs-row button {
    background-color: #4CAF50; /* Green */
    color: white;
    border: none;
    padding: 5px 10px;
    font-size: 14px;
    border-radius: 4px;
    cursor: pointer;
  }
  
  .inputs-row button:hover {
    background-color: #45a049;
  }
  
  .msform-note {
    font-size: 14px;
    color: white;
    margin-top: 5px; /* Add spacing above the note */
  }
  