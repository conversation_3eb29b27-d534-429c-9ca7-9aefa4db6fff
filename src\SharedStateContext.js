import React, { createContext, useContext, useReducer } from 'react';
import Patient from './models/PatientData';
import MICData from './models/MICData';
// 定义初始状态
const initialState = {
  patient: null,
  mic: null,
  notifications: []
};

// 定义 reducer 函数
const reducer = (state, action) => {
  switch (action.type) {
    case 'SET_USER':
      return { ...state, patient: action.payload };
    case 'SET_MIC':
      return { ...state, mic: action.payload };
    case 'SET_SAMPLE':
      return { ...state, sample: action.payload };
    case 'ADD_NOTIFICATION':
      return { ...state, notifications: [...state.notifications, action.payload] };
    default:
      return state;
  }
};

// 创建 Context
const StateContext = createContext();

// 创建 Provider 组件
export const StateProvider = ({ children }) => {
  const [state, dispatch] = useReducer(reducer, initialState);

  return (
    <StateContext.Provider value={{ state, dispatch }}>
      {children}
    </StateContext.Provider>
  );
};

// 创建自定义 hook 来使用状态
export const useSharedState = () => useContext(StateContext);