.App {
  text-align: center;
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

.App-header {
  background-color: #282c34;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  /* justify-content: center; */
  font-size: calc(10px + 2vmin);
  color: white;
}

.App-link {
  color: #61dafb;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.ant-modal {
  text-align: center;
}

.ant-modal-title {
  font-size: 18px;
  font-weight: bold;
}

.ant-table {
  margin-top: 20px;
  font-size: 14px;
}

.ant-table th,
.ant-table td {
  text-align: center;
  vertical-align: middle;
}

.ant-btn {
  margin: 5px;
}

