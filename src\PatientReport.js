import React, { useState,useEffect } from 'react';
import './models/MICData';
import "./ReportPage.css"; 
import MICData from './models/MICData';
import printDiv from "./componets/printDiv";
import { Tooltip,Button } from 'antd';
import { Printer, Upload, Download, } from 'lucide-react';
import { useSharedState } from './SharedStateContext';
import { ShieldQuestion,Construction} from 'lucide-react';
import * as XLSX from "xlsx";
import CustomDialog from './componets/CustomDialog';


const PatientReport = () => {
 const { state, dispatch } = useSharedState();  
 const [hintMessage,setHintMessage] = useState('');
 const [specimenNumber,setSpecimenNumber] = useState('');
 const [existRecords,setExistRecords] = useState([]);
 const [dialogVisible, setDialogVisible] = useState(false);
 
 useEffect(() => {
  if (state.patient && state.mic) {
   
    const matchingRecords = state.patient.getTestData().filter(
      (data) => data.specimenNumber === state.mic[0]?.specimenNumber
    );
   
    setExistRecords((prevRecords) => {
      // Avoid overwriting with an empty array unless intentional
      if (matchingRecords.length > 0) {
        // console.log("Updating existRecords with matching records:", matchingRecords);
        return matchingRecords;
      }
      // console.warn("No matching records found, keeping existing records.");
      return prevRecords; // Keep previous state if no match is found
    });
  }
}, [state.mic, state.patient]);



const DeductionItem = ({ deduction, index }) => {
  const [showFull, setShowFull] = useState(false); // useState 必须始终被调用

  if (!deduction || typeof deduction !== "string") return null;

  // 解析文本
  const match = deduction.match(/（1）(.*?)(（2）|$)/s);
  const shortText = match ? match[1].trim() : deduction; 

  return (
    <div className="remark-input" style={{ width: "100%" }}>
      <span>{shortText}</span>
      {!showFull && (
        <Button type="link" onClick={() => setShowFull(true)}>详细...</Button>
      )}
      {showFull && (
        <div style={{ marginTop: "5px", whiteSpace: "pre-wrap" }}>{deduction}</div>
      )}
    </div>
  );
};






 const handleImport = (e) => {
 
 const file = e.target.files[0]; // 获取上传的文件

 if (!file) return;
 const reader = new FileReader();
 let Mic = [];
  // 当文件加载完毕时
  reader.onload = (event) => {
    const binaryStr = event.target.result;
    const workbook = XLSX.read(binaryStr, { type: "binary" });
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
    const jsonData = XLSX.utils.sheet_to_json(worksheet);
   
    jsonData.map((result)=>{
    
     if(result.patient_id!=state.patient.patient_id){
       setHintMessage("用户id 不匹配");
       setDialogVisible(true);
       return;
     }
       let micData = new MICData();
       micData.Agent = result.agent;
       micData.ECV = result.ECV;
       micData.Bacteria = result.bacteriaName;
       micData.MIC = result.micValue;
       micData.SORT = result.sensitivity;
       micData.Remark = result.remark;
       micData.specimenNumber=result.specimenNumber;
       micData.selectedMethod=result.selectedMethod;
       
       Mic.push(micData);
     
     

    });
     // 追加新数据
     dispatch({ type: "SET_MIC", payload: Mic });
    // 清空文件输入值
    e.target.value = null;
  };

  reader.readAsBinaryString(file);
};

 const handleExport = () => {
   
 const templateUrl = `${process.env.PUBLIC_URL + "Report_template.xltx"}`;
  const link = document.createElement("a");
  link.href = templateUrl; // 指向模板路径
  link.download = "Report_template.xltx";
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

const handlewarType=(wardType)=>{
  switch(wardType){
    case "in":
      return "住院";
    case "out":
      return "出院";
    case "eme":
      return "急诊";
    case "icu":
      return "ICU";
    default:
      return "住院";
  }
};
  const renderRemark = (remark) => {
    if(!remark) return;
    return remark.split('\n').map((text, index) => (
      <p key={index}>{text}</p>
    ));
  };

  const handleClose = () => {
    setDialogVisible(false);
  };


  if(!state.patient)
  return (
    <div className="report-container">
      <div className="patient-selection-content">
        <ShieldQuestion size={35} />
        <span className="patient-selection-text">还没有选择标本信息!!</span>
      </div>
    </div>
  );
  
  
  if(!state.mic)
  
  return (
    <div className="report-container">
      <div className="patient-selection-content">
        <Construction size={35} />
        <span className="patient-selection-text">还没有生成报告,请转到'Mic折点',查询后，点击生成报告!!</span>
      </div>
    </div>
  )
 


  return (
    <div className="report-container">
      
       <div>
          <h2 className="title">复旦大学附属华山医院临床微生物室药敏报告单</h2>
          <div className="patient-info">
            <p>姓名: {state.patient.name}</p>
            <p>性别:{state.patient.gender==1?"男":"女"}</p>
            <p>年龄: {state.patient.age}</p>
            <p>标本类型: {state.patient.sampleType}</p>
          </div>

          <div className="info-line" >
            <p>科室: {state.patient.department}</p>
            <p>病房: {handlewarType(state.patient.ward)}</p>
            <p>病床: {state.patient.bed}</p>
            <p>病历号: {state.patient.medicalRecordNumber}</p>
          </div>
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
    <p className="result" style={{ textAlign: 'left' }}>涂片结果:{state.patient.smearResult}</p>
    <p className="result" style={{ textAlign: 'center' }}>菌落计数:{state.patient.bacteriaCount}</p>
    <p className="result" style={{ textAlign: 'right' }}>培养结果:{state.patient.cultureResult}</p>
</div>
       
<table className="antibiotic-table" >
<thead>
              <tr>
                <th>实验方法</th>
                 <th>解析结果</th>
                
              </tr>
            </thead>
            <tbody>
  {state.patient.getTestData().map((record, index) => (
    <React.Fragment key={`patient-test-${index}`}>
      {record.testMethod && (  <tr>
        <td>{record.testMethod}</td>
        <td>{record.testResult}</td>
      </tr>)}
    
      {record.testSupplement && (
        <tr>
          <td>{record.testSupplement}</td>
          <td>{record.testSupplementResult}</td>
        </tr>
      )}
    </React.Fragment>
  ))}
</tbody>

  </table>
        
  <table className="antibiotic-table">
  <thead>
    <tr>
      <th>抗菌药物</th>
      <th>折点或ECV (mg/L)</th>
      {state.mic.some(record => record.DISK) && <th>纸片含量 (μg)</th>} {/* Only show if at least one record has a DISK value */}
      <th>MIC (mg/L)</th>
      <th>耐药结论</th>
    </tr>
  </thead>
  <tbody>
    {/* Map through MIC data */}
    {[...new Map(state.mic.map(record => [record.Agent, record])).values()]
  .map((record, index) => (
    <tr key={`mic-record-${index}`}>
      <td>{record.Agent}</td> 
      <td>{record.ECV}</td>
      {record.DISK && <td>{record.DISK}</td>} 
      <td>{record.MIC}</td>
      <td>{record.SORT}</td>
    </tr>
  ))
}

  </tbody>
</table>
<div className="notes">
  {/* Track a total index across all loops */}
  {(() => {
    let totalIndex = 0; // Initialize a total counter
    
    return (
      <>
        {/* Loop for Comments */}
        {[...new Set(state.mic.map(item => item.Comments))] // 去重
          .filter(comment => 
            comment && // Ensure comment is not empty
            state.mic.some(item => comment.includes(item.selectedAuthority)) && // Check for selectedAuthority
            /[\u4e00-\u9fa5]/.test(comment) // Ensure it contains Chinese characters
          )
          .map((comment, index) => {
            totalIndex++; // Increment the total index for each non-empty comment
            return (
              <div key={`comment-${totalIndex}`} className="remark-input" style={{ width: '100%' }}>
                {totalIndex}. {comment}
              </div>
            );
          })
        }

        {/* Loop for Remarks */}
        {[...new Set(state.mic.map(item => item.Remark))] 
          .filter(remark => remark) // Filter out empty remarks
          .map((remark, index) => {
            totalIndex++; // Increment the total index for each valid remark
            return (
              <div key={`remark-${totalIndex}`} className="remark-input" style={{ width: '100%' }}>
                {totalIndex}. {remark}
              </div>
            );
          })
        }

        {/* Loop for drugDeduction */}
        {[...new Set(state.mic.map(item => item.drugDeduction))] 
          .filter(deduction => deduction) // Filter out empty deductions
          .map((deduction, index) => {
            totalIndex++; // Increment the total index for each valid deduction
            return (
              <DeductionItem key={`deduction-${totalIndex}`} deduction={deduction} index={totalIndex} />
            );
          })
        }

        {/* Loop for ESBL Alerts */}
        {[...new Set(state.mic.map(item => item.esblAlert))] 
          .filter(esblAlert => esblAlert) // Filter out empty alerts
          .map((esblAlert, index) => {
            totalIndex++; // Increment the total index for each valid ESBL alert
            return (
              <div key={`esblAlert-${totalIndex}`} className="remark-input" style={{ width: '100%' }}>
                {totalIndex}. {esblAlert}
              </div>
            );
          })
        }

        {/* Loop for expertics (ABX_RESULT and MICROBIOL) */}
        {
          [
            ...new Set(
              state.mic.flatMap(item =>
                Array.isArray(item.expertics)
                  ? item.expertics.map(expertic =>
                      JSON.stringify({
                        ABX_RESULT: expertic.ABX_RESULT,
                        MICROBIOL: expertic.MICROBIOL
                      })
                    )
                  : []
              )
            )
          ]
          .filter(entry => entry)
          .map((uniqueEntryString, index) => {
            totalIndex++;
            const uniqueEntry = JSON.parse(uniqueEntryString);
            return (
              <div key={`expertic-${totalIndex}`} className="remark-input" style={{ width: '100%' }}>
                {totalIndex}. {uniqueEntry.ABX_RESULT} : {uniqueEntry.MICROBIOL}
              </div>
            );
          })
        }

              </>
            );
        })()}
</div>




              <div>
            <Tooltip title="Print Report">
              <Printer
                size={24}
                onClick={() => printDiv('report-container')}
                style={{ cursor: 'pointer' }}
                className="no-print mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
              >
                Print Report
              </Printer>
            </Tooltip>
            &nbsp;&nbsp;&nbsp;

            <Tooltip title="Download Template">
              <Download
                size={24}
                onClick={() => handleExport()}
                style={{ cursor: 'pointer' }}
                className="no-print mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
              >
                Download Template
              </Download>
            </Tooltip>

            <input
              type="file"
              accept=".xlsx, .xls"
              onChange={handleImport}
              style={{ display: "none" }}
              id="file-input" />
            <button
              onClick={() => document.getElementById("file-input").click()}
              style={{ background: "none", border: "none", cursor: "pointer" }}
            >
              <Tooltip title="Upload Data">
                <Upload size={24} style={{ cursor: 'pointer',color:'black' }}
                  className="no-print mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                  上传数据
                </Upload>
              </Tooltip>
            </button>
            {dialogVisible && (
              <CustomDialog
                message={hintMessage}
                onClose={handleClose} />
            )}
          </div>
       </div>
      
    </div>
  ) 
};

export default PatientReport;
