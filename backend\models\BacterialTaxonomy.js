const mongoose = require('mongoose');

const bacterialTaxonomySchema  = new mongoose.Schema({
  'name': { type: String, required: true },
  'level': { type: String },
  'parent_id': { type: String }, // 对应的单位是毫米
  children: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'BacterialTaxonomy'
}]
}, { collection: 'BacterialTaxonomy' });

module.exports = mongoose.model('BacterialTaxonomy', bacterialTaxonomySchema);
