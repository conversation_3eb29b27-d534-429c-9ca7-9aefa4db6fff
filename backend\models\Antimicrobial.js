const mongoose = require('mongoose');

const antimicrobialSchema  = new mongoose.Schema({
  'Antimicrobial Agent': { type: String, required: true },
  'Disk Content': { type: String },
  'S (mm)': { type: String },
  'SDD (mm)': { type: String },
  'I (mm)': { type: String },
  'R (mm)': { type: String },
  'S (μg/mL)': { type: String },
  'SDD (μg/mL)': { type: String },
  'I (μg/mL)': { type: String },
  'R (μg/mL)': { type: String },
  'BacterId': { type: String },
  'Comments': { type: String },
  'newId': { type: String },
  'nickName': { type: String },
  'antibioticCode': { type: String},
  'bacteriaName': { type: String},
  create_date: { type: Date, default: Date.now },
  update_date: { type: Date, default: Date.now },
}, { collection: 'breaking' });

// Automatically update 'update_date' on save
antimicrobialSchema.pre('save', function (next) {
  this.update_date = Date.now();
  next();
});

module.exports = mongoose.model('Antimicrobial', antimicrobialSchema);