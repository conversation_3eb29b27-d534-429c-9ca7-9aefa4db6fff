import React from 'react';
import './Homepage.css'; // 引入样式

const Homepage = () => {
    return (
        <div className="homepage" style={{ backgroundImage: `url(${process.env.PUBLIC_URL + "/bk-Photoroom.png"})` }}>
         <h3 className='title' >药敏专家系统</h3>
         <h6 className='subtitle'>Antimicrobial Sensitivity Expert System</h6>
              
            <div className="content">
                <div className="company-info">
                  <p>药敏专家系统由艾尔法智慧医疗科技（上海）有限公司设计开发，是一款用于医学微生物实验和抗菌药物敏感性分析的工具。
                    它集成了患者信息管理、实验样本记录、敏感性折点查询以及自动报告生成等功能，帮助实验室和临床医生高效分析微生物检测结果，支持科学决策。
                    </p>
                    <p>
                    这款软件的功能设计旨在帮助医学实验室和临床医生更高效地进行微生物和抗菌药物的检测与分析，支持科学的实验数据管理和医学报告生成，提升工作效率和准确性
                    </p>
                </div>
            </div>
            <footer className="footer">
                <p>©2024 alphasmartmedical.com. 版权所有</p>
            </footer>
        </div>
    );
};

export default Homepage;
