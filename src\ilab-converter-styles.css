.converter-container {
    max-width: 1200px;
    margin: 20px auto;
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  .converter-title {
    font-size: 24px;
    margin-bottom: 20px;
    color: #333;
  }
  
  .button-group {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
  }
  
  .btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    background: #f0f0f0;
  }
  
  .btn:hover {
    background: #e0e0e0;
  }
  
  .btn-primary {
    background: #1677ff;
    color: white;
  }
  
  .btn-primary:hover {
    background: #4096ff;
  }
  
  .btn-danger {
    background: #ff4d4f;
    color: white;
  }
  
  .btn-danger:hover {
    background: #ff7875;
  }
  
  .input-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 10px;
    margin-bottom: 20px;
  }
  
  .input-field {
    padding: 8px;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    width: 100%;
  }
  
  .input-field:focus {
    outline: none;
    border-color: #1677ff;
  }
  
  .table-container {
    overflow-x: auto;
    margin-top: 20px;
  }
  
  .data-table {
    width: 100%;
    border-collapse: collapse;
    text-align: left;
  }
  
  .data-table th,
  .data-table td {
    padding: 12px 8px;
    border-bottom: 1px solid #f0f0f0;
   
  }
  
  .data-table th {
    background: #fafafa;
    font-weight: 500;
  }
  
  .data-table tr:hover {
    background: #fafafa;
  }
  
  .upload-input {
    display: none;
  }
  
  .error-message {
    color: #ff4d4f;
    margin: 10px 0;
  }
  
  @media (max-width: 768px) {
    .input-grid {
      grid-template-columns: 1fr;
    }
    
    .button-group {
      flex-direction: column;
    }
  }