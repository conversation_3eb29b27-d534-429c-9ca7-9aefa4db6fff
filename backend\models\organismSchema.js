const mongoose = require('mongoose');

const organismSchema = new mongoose.Schema({
  ORGANISM_CODE: {
    type: String,
    required: true,
    unique: true,
    maxlength: 3,
  },
  DISPLAY_NAME: {
    type: String,
    required: true,
    maxlength: 50,
  },
 
  CREATE_TIME: {
    type: Date,
    default: Date.now,
    required: true,
  },
  UPDATE_TIME: {
    type: Date,
    default: Date.now,
    required: true,
  }
}, {
  timestamps: { createdAt: 'CREATE_TIME', updatedAt: 'UPDATE_TIME' }, 
  collection: 't_dict_organism'
});
module.exports = mongoose.model('OrganismModel', organismSchema);
