/* QueryForm.css */
.container {
  width: 100%;
  max-width: 650px; /* 设置最大宽度 */
  margin: 0 auto;
  padding: 20px;
  box-sizing: border-box; /* 确保内边距不影响整体宽度 */
}

 .scrollableContainer{
  width: 100%;
  max-height: 300px;
  overflow: 'auto';
  border: '1px solid #ccc';
  border-radius: 5px;
  padding: 10px;
  margin-top: 10px;
  background-color: '#f9f9f9';
  color: '#333';
  font-size: '14px';
} 

.panel {
  border: 1px solid #0056b3;
  border-radius: 8px;
  background-color: #282c34;
  padding: 20px;
  margin-top: 20px;
  height: 350px; /* Fixed height */
  overflow-y: auto; /* Adds vertical scroll if content exceeds height */
  overflow-x: hidden; /* Prevents horizontal scrolling */
}
.panel::-webkit-scrollbar {
  width: 8px;
}

.panel::-webkit-scrollbar-track {
  background: #444;
  border-radius: 8px;
}

.panel::-webkit-scrollbar-thumb {
  background-color: #0056b3;
  border-radius: 8px;
  border: 2px solid #282c34;
}


.query-form {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 20px;
}

.form-group {
  display: flex;
  justify-content: space-between;
  align-items: center;
 
}

.form-group label {
  width: 120px;
}

.form-group input {
  flex: 1;
  padding: 5px;
}

.form-label {
  width: 100px;
  margin-right: 10px;
  text-align: right;
  font-size: 14px;
  color:#f0f0f0;
}

.submit-button-container {
  display: flex;
  justify-content: center; /* 使按钮居中 */
  margin-top: 10px; /* 为按钮添加顶部间距 */
}

.submit-button {
  background-color: #4CAF50; /* 绿色背景 */
  color: white; /* 白色文字 */
  padding: 10px 20px; /* 内边距 */
  border: none; /* 无边框 */
  border-radius: 5px; /* 圆角 */
  cursor: pointer; /* 鼠标悬停效果 */
  font-size: 16px; /* 字体大小 */
  margin-top: 10px; /* 顶部边距 */
  transition: background-color 0.3s; /* 背景色过渡效果 */
 
}



.submit-button:hover {
  background-color: #0056b3;
}

.result-table-container {
  width: 100%; /* 确保容器与表单一致 */
  overflow-x: auto; /* 如果表格太宽，允许水平滚动 */
}

.result-table {
  width: 100%; /* 设置表格宽度与容器一致 */
  border-collapse: collapse;
  margin-top: 10px;
  color: #ccc;
}

.result-table th,
.result-table td {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: left;
  font-size: 14px;
}



.suggestions-list {
  position: absolute;
  top: 100%; /* 位于输入框正下方 */
  left: 0;
  width: 100%; /* 与输入框宽度一致 */
  max-height: 200px; /* 最大高度，可根据需要调整 */
  overflow-y: auto; /* 允许垂直滚动 */
  background-color: #f9f9f9; /* 背景色 */
  border: 1px solid #ccc; /* 边框 */
  border-radius: 4px; /* 圆角效果 */
  padding: 0;
  margin: 0;
  list-style: none; /* 去掉列表项默认样式 */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); /* 阴影效果 */
  z-index: 1000; /* 确保列表在输入框之上 */
}
.suggestion-item {
  padding: 8px;
  font-size: 14px; /* 较小的字体 */
  cursor: pointer;
  color: #333; /* 文字颜色 */
  border-bottom: 1px solid #ddd; /* 分隔线 */
  background-color: #fff; /* 建议项背景色 */
}

.suggestion-item:hover {
  background-color: #f0f0f0; /* 鼠标悬停时的背景色 */
}



.suggestion-item:last-child {
  border-bottom: none; /* 去掉最后一个项的分隔线 */
}
.mic-input,
.agent-select {
 
  width: 80%; /* Makes the select box as wide as its container */
  padding:8px; /* Adds padding inside the select box */
  font-size: 14px; /* Sets a readable font size */
  border: 1px solid #ccc; /* Adds a light gray border */
  border-radius: 4px; /* Rounds the corners of the select box */
  background-color: #f9f9f9; /* Light gray background */
  color: #333; /* Darker text color */
  box-sizing: border-box;
}

.agent-select:focus {
  border-color: #66afe9; /* Changes the border color on focus */
  outline: none; /* Removes the default outline */
  box-shadow: 0 0 5px rgba(102, 175, 233, 0.6); /* Adds a subtle shadow on focus */
}

.reset-button {
  background-color: #3b82f6;
  color: white; /* 白色文字 */
  padding: 10px 20px; /* 内边距 */
  border: none; /* 无边框 */
  border-radius: 5px; /* 圆角 */
  cursor: pointer; /* 鼠标悬停效果 */
  font-size: 16px; /* 字体大小 */
  margin-top: 10px; /* 顶部边距 */
  transition: background-color 0.3s; /* 背景色过渡效果 */
  margin-left: 10px; 
}

.reset-button:hover {
  background-color: #c82333;
}

.radio-group {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.radio-label {
  margin-right: 10px; /* Adjust this value to control spacing */
}

.radio-group label {
  margin-right: 5px;
}


.help-container {
  display: flex;
  justify-content: center; /* Centers the list horizontally */
  align-items: center;     /* Centers the list vertically if the container has a height */
  min-height: 100vh;       /* Ensures the container takes up the full height of the viewport */
  text-align: left;        /* Ensures the text within the list is left-aligned */
}

.help-list {
  list-style-type: none;   /* Removes default list bullets */
  padding: 0;              /* Removes default padding */
  margin: 0;               /* Removes default margin */
  font-size: 24px;
}

.help-list li {
  position: relative;
  padding-left: 25px; /* Increased space for the arrow */
}

.help-list li:before {
  content: "➔"; /* Unicode arrow character */
  position: absolute;
  left: 0;
  top: 0; /* Aligns the arrow with the text */
  color: green; /* Arrow color changed to green */
}

.homepage-container {
  text-align: center; /* Center all text and content */
  padding: 20px;
  font-family: "Arial", sans-serif;
}

.homepage-title {
  font-size: 2.5em;
  margin-bottom: 20px;
  color: #333; /* Dark gray color for the title */
}

.homepage-welcome {
  font-size: 1.5em;
  margin-bottom: 30px;
  color: #555; /* Slightly lighter gray for the welcome message */
}

.homepage-image {
  width: 400px; /* Set the image width to 400 pixels */
  height: auto; /* Maintain the aspect ratio */
  margin: 0 auto 40px; /* Center the image horizontally with a bottom margin */
  display: block; /* Make sure the image is treated as a block element */
  border-radius: 10px; /* Slightly rounded corners for the image */
}

.homepage-footer {
  margin-top: 40px;
  font-size: 0.9em;
  color: #777; /* Even lighter gray for the footer */
}

.input-button-group {
  display: flex;
  align-items: center; /* Align items vertically */
  width: 100%; /* Ensure it spans the entire form width */
}



.submit-button, .import-button {
  padding: 8px;
  box-sizing: border-box;
  background-color:  #10b981;; /* Matches submit button color */
  color: white;
  border: none;
  cursor: pointer;
  transition: background-color 0.3s ease;
  font-size: 1rem;
}

.submit-button:hover, .import-button:hover {
  background-color: #218838;
}


.delete-button, .import-button {
  padding: 8px;
  box-sizing: border-box;
  background-color: #ef4444;/* Matches submit button color */
  color: white;
  border: none;
  cursor: pointer;
  transition: background-color 0.3s ease;
  font-size: 1rem;
}

.delete-button:hover, .import-button:hover {
  background-color: #218838;
}


.import-button {
  margin-left: 10px;
  height: 100%; /* Match height of input */
}

.form-note {
  margin-left: 200px;
  color: white;
  font-size: 0.85rem; /* Adjust font size */
}


.delete-button {
  background-color:#ef4444;;
  color: white; /* 白色文字 */
  padding: 10px 20px; /* 内边距 */
  border: none; /* 无边框 */
  border-radius: 5px; /* 圆角 */
  cursor: pointer; /* 鼠标悬停效果 */
  font-size: 16px; /* 字体大小 */
  margin-top: 10px; /* 顶部边距 */
  transition: background-color 0.3s; /* 背景色过渡效果 */
  margin-left: 10px; 
}



.submit-button {
  background-color: #10b981;
  color: white; /* 白色文字 */
  padding: 10px 20px; /* 内边距 */
  border: none; /* 无边框 */
  border-radius: 5px; /* 圆角 */
  cursor: pointer; /* 鼠标悬停效果 */
  font-size: 16px; /* 字体大小 */
  margin-top: 10px; /* 顶部边距 */
  transition: background-color 0.3s; /* 背景色过渡效果 */
  margin-left: 10px; 
}

.import-button {
  background-color: #8b5cf6; /* Violet/purple background */
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s;
  width: 40px;  /* Fixed width to match icon size */
  height: 40px; /* Fixed height to match icon size */
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0; /* Remove padding since we're using flex centering */
}

.import-button:hover {
  background-color: #7c3aed; /* Slightly darker purple on hover */
}

.view-button {
  background-color: #06b6d4;
  color: white; /* 白色文字 */
  padding: 6px 12px;
  font-size: 14px;/* 内边距 */
  border: none; /* 无边框 */
  border-radius: 5px; /* 圆角 */
  cursor: pointer; /* 鼠标悬停效果 */
  margin-top: 10px; /* 顶部边距 */
  transition: background-color 0.3s; /* 背景色过渡效果 */
  margin-left: 10px; 
}

.ant-tabs-tab {
  color: white !important; /* Ensure the tab text is white */
  font-size: 14px; /* Adjust font size if needed */
  font-weight: bold; /* Optional for better visibility */
}

.ant-tabs-tab-active {
  background-color: #007bff; /* Active tab background color */
  border-radius: 4px 4px 0 0; /* Rounded top corners */
}

.ant-tabs-nav .ant-tabs-tab:hover {
  color: #cccccc !important; /* Slightly lighter text color on hover */
}

.ant-tabs-ink-bar {
  background-color: white !important; /* Make the active underline white */
}
.patient-form {
  margin: 20px;
}

td {
  max-width: 300px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}


